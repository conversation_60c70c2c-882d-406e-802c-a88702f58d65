<?php
namespace MyProject;

use Single\ClassA as A;
use Grouped\Classes\ClassB;
use Grouped\Classes\ClassC as C;
use const Grouped\Constants\D;
use const Grouped\Constants\E;
use Single\ClassF;
use Grouped\Mixed\ClassG;
use const Grouped\Mixed\ConstH;
use function Grouped\Mixed\func_i;
use Grouped\Mixed\ClassJ as J;
use function Grouped\Functions\func_k;

// Some examples from the original RFC.
use function foo\math\sin;
use function foo\math\cos;
use function foo\math\cosh;
use const foo\math\PI;
use const foo\math\E;
use const foo\math\GAMMA;
use const foo\math\GOLDEN_RATIO;
use foo\math\Math;
use const foo\math\PI;
use function foo\math\sin;
use function foo\math\cos;
use function foo\math\cosh;

use Grouped\TrailingComma\ClassL;
use const Grouped\TrailingComma\ConstM;
use function Grouped\TrailingComma\func_n;
use Grouped\TrailingComma\ClassO as O;

use function foo\math\trailingcomma\no\whitespace\sin;
use function foo\math\trailingcomma\no\whitespace\cos;
use function foo\math\trailingcomma\no\whitespace\cosh;

use function foo\math\multipleonnewline\sin;
use function foo\math\multipleonnewline\cos; /* comment */
use function foo\math\multipleonnewline\cosh;

// phpcs:disable Standard.Cat.Sniff -- for reasons.
    // A comment.
use Grouped\TrailingCommaWithCommentsAndAnnotations\ClassP;
    /* Another comment. */
use const Grouped\TrailingCommaWithCommentsAndAnnotations\ConstQ; // A trailing comment.

use function Grouped\TrailingCommaWithCommentsAndAnnotations\func_r; // phpcs:ignore Standard.Cat.Sniff -- for reasons.
    // phpcs:ignore Standard.Cat.Sniff -- for reasons.
use Grouped\TrailingCommaWithCommentsAndAnnotations\ClassS as S;

    // Some other comment.


// phpcs:enable
