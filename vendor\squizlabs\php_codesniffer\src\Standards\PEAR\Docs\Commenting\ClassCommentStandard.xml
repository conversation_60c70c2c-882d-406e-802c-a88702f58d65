<documentation title="Class Comments">
    <standard>
    <![CDATA[
    Classes and interfaces must have a non-empty doc comment.  The short description must be on the second line of the comment.  Each description must have one blank comment line before and after.  There must be one blank line before the tags in the comments.  A @version tag must be in Release: package_version format.
    ]]>
    </standard>
    <code_comparison>
        <code title="Valid: A doc comment for the class.">
        <![CDATA[
<em>/**
 * The Foo class.
 */</em>
class Foo
{
}
        ]]>
        </code>
        <code title="Invalid: No doc comment for the class.">
        <![CDATA[
class Foo
{
}
        ]]>
        </code>
    </code_comparison>
    <code_comparison>
        <code title="Valid: A doc comment for the class.">
        <![CDATA[
<em>/**
 * The Foo class.
 */</em>
class Foo
{
}
        ]]>
        </code>
        <code title="Invalid: Invalid comment type for the class.">
        <![CDATA[
// The Foo class.
class Foo
{
}
        ]]>
        </code>
    </code_comparison>
    <code_comparison>
        <code title="Valid: A doc comment for the class.">
        <![CDATA[
<em>/**
 * The Foo class.
 */</em>
class Foo
{
}
        ]]>
        </code>
        <code title="Invalid: The blank line after the comment makes it appear as a file comment, not a class comment.">
        <![CDATA[
<em>/**
 * The Foo class.
 */</em>

class Foo
{
}
        ]]>
        </code>
    </code_comparison>
    <code_comparison>
        <code title="Valid: Short description is the second line of the comment.">
        <![CDATA[
/**
 * <em>The Foo class.</em>
 */
class Foo
{
}
        ]]>
        </code>
        <code title="Invalid: An extra blank line before the short description.">
        <![CDATA[
/**
 *
 * <em>The Foo class.</em>
 */
class Foo
{
}
        ]]>
        </code>
    </code_comparison>
    <code_comparison>
        <code title="Valid: Exactly one blank line around descriptions.">
        <![CDATA[
/**
 * The Foo class.
 * <em></em>
 * A helper for the Bar class.
 * <em></em>
 * @see Bar
 */
class Foo
{
}
        ]]>
        </code>
        <code title="Invalid: Extra blank lines around the descriptions.">
        <![CDATA[
/**
 * The Foo class.
 * <em></em>
 * <em></em>
 * A helper for the Bar class.
 * <em></em>
 * <em></em>
 * @see Bar
 */
class Foo
{
}
        ]]>
        </code>
    </code_comparison>
    <code_comparison>
        <code title="Valid: Exactly one blank line before the tags.">
        <![CDATA[
/**
 * The Foo class.
 * <em></em>
 * @see Bar
 */
class Foo
{
}
        ]]>
        </code>
        <code title="Invalid: Extra blank lines before the tags.">
        <![CDATA[
/**
 * The Foo class.
 * <em></em>
 * <em></em>
 * @see Bar
 */
class Foo
{
}
        ]]>
        </code>
    </code_comparison>
    <code_comparison>
        <code title="Valid: Version tag is in the correct format.">
        <![CDATA[
/**
 * The Foo class.
 *
 * @version <em>Release: 1.0</em>
 */
class Foo
{
}
        ]]>
        </code>
        <code title="Invalid: No Release: text.">
        <![CDATA[
/**
 * The Foo class.
 *
 * @version <em>1.0</em>
 */
class Foo
{
}
        ]]>
        </code>
    </code_comparison>
</documentation>
