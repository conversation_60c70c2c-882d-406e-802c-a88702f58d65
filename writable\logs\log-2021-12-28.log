CRITICAL - 2021-12-28 00:17:00 --> Call to undefined function App\Controllers\Admin\uploadSalonservice()
#0 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Salonservice->savesalonservice()
#1 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Salonservice))
#2 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#3 E:\xampp\htdocs\PROJECTS\baidees\index.php(48): CodeIgniter\CodeIgniter->run()
#4 {main}
CRITICAL - 2021-12-28 04:00:41 --> Cannot redeclare App\Models\FrontendModel::getSalonServices()
#0 [internal function]: CodeIgniter\Debug\Exceptions->shutdownHandler()
#1 {main}
CRITICAL - 2021-12-28 04:01:41 --> Too few arguments to function App\Models\FrontendModel::getallSalonServices(), 1 passed in E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Frontend.php on line 74 and exactly 3 expected
#0 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Frontend.php(74): App\Models\FrontendModel->getallSalonServices('Male')
#1 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Frontend->index()
#2 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#3 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#4 E:\xampp\htdocs\PROJECTS\baidees\index.php(48): CodeIgniter\CodeIgniter->run()
#5 {main}
CRITICAL - 2021-12-28 04:01:42 --> Too few arguments to function App\Models\FrontendModel::getallSalonServices(), 1 passed in E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Frontend.php on line 74 and exactly 3 expected
#0 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Frontend.php(74): App\Models\FrontendModel->getallSalonServices('Male')
#1 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Frontend->index()
#2 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#3 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#4 E:\xampp\htdocs\PROJECTS\baidees\index.php(48): CodeIgniter\CodeIgniter->run()
#5 {main}
ERROR - 2021-12-28 05:59:30 --> mysqli_sql_exception: Unknown column 'service_id' in 'field list' in E:\xampp\htdocs\PROJECTS\baidees\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\xampp\htdocs\PROJECTS\baidees\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `service...')
#1 E:\xampp\htdocs\PROJECTS\baidees\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `service...')
#2 E:\xampp\htdocs\PROJECTS\baidees\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `service...')
#3 E:\xampp\htdocs\PROJECTS\baidees\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `service...', Array, false)
#4 E:\xampp\htdocs\PROJECTS\baidees\app\Models\FrontendModel.php(347): CodeIgniter\Database\BaseBuilder->get()
#5 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Frontend.php(1055): App\Models\FrontendModel->getFirstSalonServices()
#6 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Frontend->salonservices('')
#7 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\xampp\htdocs\PROJECTS\baidees\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2021-12-28 05:59:34 --> mysqli_sql_exception: Unknown column 'service_id' in 'field list' in E:\xampp\htdocs\PROJECTS\baidees\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\xampp\htdocs\PROJECTS\baidees\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `service...')
#1 E:\xampp\htdocs\PROJECTS\baidees\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `service...')
#2 E:\xampp\htdocs\PROJECTS\baidees\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `service...')
#3 E:\xampp\htdocs\PROJECTS\baidees\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `service...', Array, false)
#4 E:\xampp\htdocs\PROJECTS\baidees\app\Models\FrontendModel.php(347): CodeIgniter\Database\BaseBuilder->get()
#5 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Frontend.php(1055): App\Models\FrontendModel->getFirstSalonServices()
#6 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Frontend->salonservices('')
#7 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\xampp\htdocs\PROJECTS\baidees\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2021-12-28 05:59:35 --> mysqli_sql_exception: Unknown column 'service_id' in 'field list' in E:\xampp\htdocs\PROJECTS\baidees\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\xampp\htdocs\PROJECTS\baidees\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `service...')
#1 E:\xampp\htdocs\PROJECTS\baidees\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `service...')
#2 E:\xampp\htdocs\PROJECTS\baidees\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `service...')
#3 E:\xampp\htdocs\PROJECTS\baidees\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `service...', Array, false)
#4 E:\xampp\htdocs\PROJECTS\baidees\app\Models\FrontendModel.php(347): CodeIgniter\Database\BaseBuilder->get()
#5 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Frontend.php(1055): App\Models\FrontendModel->getFirstSalonServices()
#6 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Frontend->salonservices('')
#7 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\xampp\htdocs\PROJECTS\baidees\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2021-12-28 05:59:38 --> mysqli_sql_exception: Unknown column 'service_id' in 'field list' in E:\xampp\htdocs\PROJECTS\baidees\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\xampp\htdocs\PROJECTS\baidees\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `service...')
#1 E:\xampp\htdocs\PROJECTS\baidees\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `service...')
#2 E:\xampp\htdocs\PROJECTS\baidees\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `service...')
#3 E:\xampp\htdocs\PROJECTS\baidees\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `service...', Array, false)
#4 E:\xampp\htdocs\PROJECTS\baidees\app\Models\FrontendModel.php(347): CodeIgniter\Database\BaseBuilder->get()
#5 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Frontend.php(1055): App\Models\FrontendModel->getFirstSalonServices()
#6 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Frontend->salonservices('')
#7 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\xampp\htdocs\PROJECTS\baidees\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2021-12-28 06:41:56 --> mysqli_sql_exception: Unknown column 'gender' in 'field list' in /home1/hitsicik/thebaidees.com/demo/system/Database/MySQLi/Connection.php:314
Stack trace:
#0 /home1/hitsicik/thebaidees.com/demo/system/Database/MySQLi/Connection.php(314): mysqli->query('SELECT `service...')
#1 /home1/hitsicik/thebaidees.com/demo/system/Database/BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `service...')
#2 /home1/hitsicik/thebaidees.com/demo/system/Database/BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `service...')
#3 /home1/hitsicik/thebaidees.com/demo/system/Database/BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `service...', Array, false)
#4 /home1/hitsicik/thebaidees.com/demo/app/Models/FrontendModel.php(347): CodeIgniter\Database\BaseBuilder->get()
#5 /home1/hitsicik/thebaidees.com/demo/app/Controllers/Frontend.php(74): App\Models\FrontendModel->getFirstSalonServices()
#6 /home1/hitsicik/thebaidees.com/demo/system/CodeIgniter.php(936): App\Controllers\Frontend->index()
#7 /home1/hitsicik/thebaidees.com/demo/system/CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 /home1/hitsicik/thebaidees.com/demo/system/CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 /home1/hitsicik/thebaidees.com/demo/index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2021-12-28 06:41:56 --> mysqli_sql_exception: Unknown column 'gender' in 'field list' in /home1/hitsicik/thebaidees.com/demo/system/Database/MySQLi/Connection.php:314
Stack trace:
#0 /home1/hitsicik/thebaidees.com/demo/system/Database/MySQLi/Connection.php(314): mysqli->query('SELECT DISTINCT...')
#1 /home1/hitsicik/thebaidees.com/demo/system/Database/BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT DISTINCT...')
#2 /home1/hitsicik/thebaidees.com/demo/system/Database/BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT DISTINCT...')
#3 /home1/hitsicik/thebaidees.com/demo/system/Database/BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT DISTINCT...', Array, false)
#4 /home1/hitsicik/thebaidees.com/demo/app/Models/FrontendModel.php(380): CodeIgniter\Database\BaseBuilder->get()
#5 /home1/hitsicik/thebaidees.com/demo/app/Controllers/Frontend.php(77): App\Models\FrontendModel->getMaleSalonServices()
#6 /home1/hitsicik/thebaidees.com/demo/system/CodeIgniter.php(936): App\Controllers\Frontend->index()
#7 /home1/hitsicik/thebaidees.com/demo/system/CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 /home1/hitsicik/thebaidees.com/demo/system/CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 /home1/hitsicik/thebaidees.com/demo/index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2021-12-28 06:41:56 --> mysqli_sql_exception: Unknown column 'gender' in 'field list' in /home1/hitsicik/thebaidees.com/demo/system/Database/MySQLi/Connection.php:314
Stack trace:
#0 /home1/hitsicik/thebaidees.com/demo/system/Database/MySQLi/Connection.php(314): mysqli->query('SELECT DISTINCT...')
#1 /home1/hitsicik/thebaidees.com/demo/system/Database/BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT DISTINCT...')
#2 /home1/hitsicik/thebaidees.com/demo/system/Database/BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT DISTINCT...')
#3 /home1/hitsicik/thebaidees.com/demo/system/Database/BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT DISTINCT...', Array, false)
#4 /home1/hitsicik/thebaidees.com/demo/app/Models/FrontendModel.php(394): CodeIgniter\Database\BaseBuilder->get()
#5 /home1/hitsicik/thebaidees.com/demo/app/Controllers/Frontend.php(78): App\Models\FrontendModel->getFemaleSalonServices()
#6 /home1/hitsicik/thebaidees.com/demo/system/CodeIgniter.php(936): App\Controllers\Frontend->index()
#7 /home1/hitsicik/thebaidees.com/demo/system/CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 /home1/hitsicik/thebaidees.com/demo/system/CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 /home1/hitsicik/thebaidees.com/demo/index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2021-12-28 06:41:58 --> Call to a member function getFirstProdCategory() on null
#0 /home1/hitsicik/thebaidees.com/demo/system/CodeIgniter.php(936): App\Controllers\Frontend->_404('icon_images', 'BRAND_LOGO_1639...')
#1 /home1/hitsicik/thebaidees.com/demo/system/CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#2 /home1/hitsicik/thebaidees.com/demo/system/CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#3 /home1/hitsicik/thebaidees.com/demo/index.php(48): CodeIgniter\CodeIgniter->run()
#4 {main}
CRITICAL - 2021-12-28 06:41:58 --> Call to a member function getFirstProdCategory() on null
#0 /home1/hitsicik/thebaidees.com/demo/system/CodeIgniter.php(936): App\Controllers\Frontend->_404('resizeimg', 'home_page_banne...')
#1 /home1/hitsicik/thebaidees.com/demo/system/CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#2 /home1/hitsicik/thebaidees.com/demo/system/CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#3 /home1/hitsicik/thebaidees.com/demo/index.php(48): CodeIgniter\CodeIgniter->run()
#4 {main}
CRITICAL - 2021-12-28 06:41:58 --> Call to a member function getFirstProdCategory() on null
#0 /home1/hitsicik/thebaidees.com/demo/system/CodeIgniter.php(936): App\Controllers\Frontend->_404('resizeimg', 'home_page_banne...')
#1 /home1/hitsicik/thebaidees.com/demo/system/CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#2 /home1/hitsicik/thebaidees.com/demo/system/CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#3 /home1/hitsicik/thebaidees.com/demo/index.php(48): CodeIgniter\CodeIgniter->run()
#4 {main}
CRITICAL - 2021-12-28 06:41:58 --> Call to a member function getFirstProdCategory() on null
#0 /home1/hitsicik/thebaidees.com/demo/system/CodeIgniter.php(936): App\Controllers\Frontend->_404('resizeimg', 'home_page_banne...')
#1 /home1/hitsicik/thebaidees.com/demo/system/CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#2 /home1/hitsicik/thebaidees.com/demo/system/CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#3 /home1/hitsicik/thebaidees.com/demo/index.php(48): CodeIgniter\CodeIgniter->run()
#4 {main}
CRITICAL - 2021-12-28 06:41:58 --> Call to a member function getFirstProdCategory() on null
#0 /home1/hitsicik/thebaidees.com/demo/system/CodeIgniter.php(936): App\Controllers\Frontend->_404('resizeimg', 'home_page_banne...')
#1 /home1/hitsicik/thebaidees.com/demo/system/CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#2 /home1/hitsicik/thebaidees.com/demo/system/CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#3 /home1/hitsicik/thebaidees.com/demo/index.php(48): CodeIgniter\CodeIgniter->run()
#4 {main}
CRITICAL - 2021-12-28 06:41:58 --> Call to a member function getFirstProdCategory() on null
#0 /home1/hitsicik/thebaidees.com/demo/system/CodeIgniter.php(936): App\Controllers\Frontend->_404('resizeimg', 'home_page_banne...')
#1 /home1/hitsicik/thebaidees.com/demo/system/CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#2 /home1/hitsicik/thebaidees.com/demo/system/CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#3 /home1/hitsicik/thebaidees.com/demo/index.php(48): CodeIgniter\CodeIgniter->run()
#4 {main}
ERROR - 2021-12-28 06:42:04 --> mysqli_sql_exception: Unknown column 'gender' in 'field list' in /home1/hitsicik/thebaidees.com/demo/system/Database/MySQLi/Connection.php:314
Stack trace:
#0 /home1/hitsicik/thebaidees.com/demo/system/Database/MySQLi/Connection.php(314): mysqli->query('SELECT `service...')
#1 /home1/hitsicik/thebaidees.com/demo/system/Database/BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `service...')
#2 /home1/hitsicik/thebaidees.com/demo/system/Database/BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `service...')
#3 /home1/hitsicik/thebaidees.com/demo/system/Database/BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `service...', Array, false)
#4 /home1/hitsicik/thebaidees.com/demo/app/Models/FrontendModel.php(347): CodeIgniter\Database\BaseBuilder->get()
#5 /home1/hitsicik/thebaidees.com/demo/app/Controllers/Frontend.php(74): App\Models\FrontendModel->getFirstSalonServices()
#6 /home1/hitsicik/thebaidees.com/demo/system/CodeIgniter.php(936): App\Controllers\Frontend->index()
#7 /home1/hitsicik/thebaidees.com/demo/system/CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 /home1/hitsicik/thebaidees.com/demo/system/CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 /home1/hitsicik/thebaidees.com/demo/index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2021-12-28 06:42:04 --> mysqli_sql_exception: Unknown column 'gender' in 'field list' in /home1/hitsicik/thebaidees.com/demo/system/Database/MySQLi/Connection.php:314
Stack trace:
#0 /home1/hitsicik/thebaidees.com/demo/system/Database/MySQLi/Connection.php(314): mysqli->query('SELECT DISTINCT...')
#1 /home1/hitsicik/thebaidees.com/demo/system/Database/BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT DISTINCT...')
#2 /home1/hitsicik/thebaidees.com/demo/system/Database/BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT DISTINCT...')
#3 /home1/hitsicik/thebaidees.com/demo/system/Database/BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT DISTINCT...', Array, false)
#4 /home1/hitsicik/thebaidees.com/demo/app/Models/FrontendModel.php(380): CodeIgniter\Database\BaseBuilder->get()
#5 /home1/hitsicik/thebaidees.com/demo/app/Controllers/Frontend.php(77): App\Models\FrontendModel->getMaleSalonServices()
#6 /home1/hitsicik/thebaidees.com/demo/system/CodeIgniter.php(936): App\Controllers\Frontend->index()
#7 /home1/hitsicik/thebaidees.com/demo/system/CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 /home1/hitsicik/thebaidees.com/demo/system/CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 /home1/hitsicik/thebaidees.com/demo/index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2021-12-28 06:42:04 --> mysqli_sql_exception: Unknown column 'gender' in 'field list' in /home1/hitsicik/thebaidees.com/demo/system/Database/MySQLi/Connection.php:314
Stack trace:
#0 /home1/hitsicik/thebaidees.com/demo/system/Database/MySQLi/Connection.php(314): mysqli->query('SELECT DISTINCT...')
#1 /home1/hitsicik/thebaidees.com/demo/system/Database/BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT DISTINCT...')
#2 /home1/hitsicik/thebaidees.com/demo/system/Database/BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT DISTINCT...')
#3 /home1/hitsicik/thebaidees.com/demo/system/Database/BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT DISTINCT...', Array, false)
#4 /home1/hitsicik/thebaidees.com/demo/app/Models/FrontendModel.php(394): CodeIgniter\Database\BaseBuilder->get()
#5 /home1/hitsicik/thebaidees.com/demo/app/Controllers/Frontend.php(78): App\Models\FrontendModel->getFemaleSalonServices()
#6 /home1/hitsicik/thebaidees.com/demo/system/CodeIgniter.php(936): App\Controllers\Frontend->index()
#7 /home1/hitsicik/thebaidees.com/demo/system/CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 /home1/hitsicik/thebaidees.com/demo/system/CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 /home1/hitsicik/thebaidees.com/demo/index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2021-12-28 06:42:05 --> Call to a member function getFirstProdCategory() on null
#0 /home1/hitsicik/thebaidees.com/demo/system/CodeIgniter.php(936): App\Controllers\Frontend->_404('resizeimg', 'home_page_banne...')
#1 /home1/hitsicik/thebaidees.com/demo/system/CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#2 /home1/hitsicik/thebaidees.com/demo/system/CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#3 /home1/hitsicik/thebaidees.com/demo/index.php(48): CodeIgniter\CodeIgniter->run()
#4 {main}
CRITICAL - 2021-12-28 06:42:06 --> Call to a member function getFirstProdCategory() on null
#0 /home1/hitsicik/thebaidees.com/demo/system/CodeIgniter.php(936): App\Controllers\Frontend->_404('resizeimg', 'home_page_banne...')
#1 /home1/hitsicik/thebaidees.com/demo/system/CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#2 /home1/hitsicik/thebaidees.com/demo/system/CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#3 /home1/hitsicik/thebaidees.com/demo/index.php(48): CodeIgniter\CodeIgniter->run()
#4 {main}
CRITICAL - 2021-12-28 06:42:06 --> Call to a member function getFirstProdCategory() on null
#0 /home1/hitsicik/thebaidees.com/demo/system/CodeIgniter.php(936): App\Controllers\Frontend->_404('icon_images', 'BRAND_LOGO_1639...')
#1 /home1/hitsicik/thebaidees.com/demo/system/CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#2 /home1/hitsicik/thebaidees.com/demo/system/CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#3 /home1/hitsicik/thebaidees.com/demo/index.php(48): CodeIgniter\CodeIgniter->run()
#4 {main}
CRITICAL - 2021-12-28 06:42:06 --> Call to a member function getFirstProdCategory() on null
#0 /home1/hitsicik/thebaidees.com/demo/system/CodeIgniter.php(936): App\Controllers\Frontend->_404('resizeimg', 'home_page_banne...')
#1 /home1/hitsicik/thebaidees.com/demo/system/CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#2 /home1/hitsicik/thebaidees.com/demo/system/CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#3 /home1/hitsicik/thebaidees.com/demo/index.php(48): CodeIgniter\CodeIgniter->run()
#4 {main}
CRITICAL - 2021-12-28 06:42:06 --> Call to a member function getFirstProdCategory() on null
#0 /home1/hitsicik/thebaidees.com/demo/system/CodeIgniter.php(936): App\Controllers\Frontend->_404('resizeimg', 'home_page_banne...')
#1 /home1/hitsicik/thebaidees.com/demo/system/CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#2 /home1/hitsicik/thebaidees.com/demo/system/CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#3 /home1/hitsicik/thebaidees.com/demo/index.php(48): CodeIgniter\CodeIgniter->run()
#4 {main}
CRITICAL - 2021-12-28 06:42:06 --> Call to a member function getFirstProdCategory() on null
#0 /home1/hitsicik/thebaidees.com/demo/system/CodeIgniter.php(936): App\Controllers\Frontend->_404('resizeimg', 'home_page_banne...')
#1 /home1/hitsicik/thebaidees.com/demo/system/CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#2 /home1/hitsicik/thebaidees.com/demo/system/CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#3 /home1/hitsicik/thebaidees.com/demo/index.php(48): CodeIgniter\CodeIgniter->run()
#4 {main}
ERROR - 2021-12-28 06:43:44 --> mysqli_sql_exception: Unknown column 'gender' in 'field list' in /home1/hitsicik/thebaidees.com/demo/system/Database/MySQLi/Connection.php:314
Stack trace:
#0 /home1/hitsicik/thebaidees.com/demo/system/Database/MySQLi/Connection.php(314): mysqli->query('SELECT `service...')
#1 /home1/hitsicik/thebaidees.com/demo/system/Database/BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `service...')
#2 /home1/hitsicik/thebaidees.com/demo/system/Database/BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `service...')
#3 /home1/hitsicik/thebaidees.com/demo/system/Database/BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `service...', Array, false)
#4 /home1/hitsicik/thebaidees.com/demo/app/Models/FrontendModel.php(347): CodeIgniter\Database\BaseBuilder->get()
#5 /home1/hitsicik/thebaidees.com/demo/app/Controllers/Frontend.php(74): App\Models\FrontendModel->getFirstSalonServices()
#6 /home1/hitsicik/thebaidees.com/demo/system/CodeIgniter.php(936): App\Controllers\Frontend->index()
#7 /home1/hitsicik/thebaidees.com/demo/system/CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 /home1/hitsicik/thebaidees.com/demo/system/CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 /home1/hitsicik/thebaidees.com/demo/index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2021-12-28 06:43:44 --> mysqli_sql_exception: Unknown column 'gender' in 'field list' in /home1/hitsicik/thebaidees.com/demo/system/Database/MySQLi/Connection.php:314
Stack trace:
#0 /home1/hitsicik/thebaidees.com/demo/system/Database/MySQLi/Connection.php(314): mysqli->query('SELECT DISTINCT...')
#1 /home1/hitsicik/thebaidees.com/demo/system/Database/BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT DISTINCT...')
#2 /home1/hitsicik/thebaidees.com/demo/system/Database/BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT DISTINCT...')
#3 /home1/hitsicik/thebaidees.com/demo/system/Database/BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT DISTINCT...', Array, false)
#4 /home1/hitsicik/thebaidees.com/demo/app/Models/FrontendModel.php(380): CodeIgniter\Database\BaseBuilder->get()
#5 /home1/hitsicik/thebaidees.com/demo/app/Controllers/Frontend.php(77): App\Models\FrontendModel->getMaleSalonServices()
#6 /home1/hitsicik/thebaidees.com/demo/system/CodeIgniter.php(936): App\Controllers\Frontend->index()
#7 /home1/hitsicik/thebaidees.com/demo/system/CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 /home1/hitsicik/thebaidees.com/demo/system/CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 /home1/hitsicik/thebaidees.com/demo/index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2021-12-28 06:43:44 --> mysqli_sql_exception: Unknown column 'gender' in 'field list' in /home1/hitsicik/thebaidees.com/demo/system/Database/MySQLi/Connection.php:314
Stack trace:
#0 /home1/hitsicik/thebaidees.com/demo/system/Database/MySQLi/Connection.php(314): mysqli->query('SELECT DISTINCT...')
#1 /home1/hitsicik/thebaidees.com/demo/system/Database/BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT DISTINCT...')
#2 /home1/hitsicik/thebaidees.com/demo/system/Database/BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT DISTINCT...')
#3 /home1/hitsicik/thebaidees.com/demo/system/Database/BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT DISTINCT...', Array, false)
#4 /home1/hitsicik/thebaidees.com/demo/app/Models/FrontendModel.php(394): CodeIgniter\Database\BaseBuilder->get()
#5 /home1/hitsicik/thebaidees.com/demo/app/Controllers/Frontend.php(78): App\Models\FrontendModel->getFemaleSalonServices()
#6 /home1/hitsicik/thebaidees.com/demo/system/CodeIgniter.php(936): App\Controllers\Frontend->index()
#7 /home1/hitsicik/thebaidees.com/demo/system/CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 /home1/hitsicik/thebaidees.com/demo/system/CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 /home1/hitsicik/thebaidees.com/demo/index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2021-12-28 06:43:46 --> Call to a member function getFirstProdCategory() on null
#0 /home1/hitsicik/thebaidees.com/demo/system/CodeIgniter.php(936): App\Controllers\Frontend->_404('icon_images', 'BRAND_LOGO_1639...')
#1 /home1/hitsicik/thebaidees.com/demo/system/CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#2 /home1/hitsicik/thebaidees.com/demo/system/CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#3 /home1/hitsicik/thebaidees.com/demo/index.php(48): CodeIgniter\CodeIgniter->run()
#4 {main}
ERROR - 2021-12-28 06:45:30 --> mysqli_sql_exception: Unknown column 'gender' in 'field list' in /home1/hitsicik/thebaidees.com/demo/system/Database/MySQLi/Connection.php:314
Stack trace:
#0 /home1/hitsicik/thebaidees.com/demo/system/Database/MySQLi/Connection.php(314): mysqli->query('SELECT `service...')
#1 /home1/hitsicik/thebaidees.com/demo/system/Database/BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `service...')
#2 /home1/hitsicik/thebaidees.com/demo/system/Database/BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `service...')
#3 /home1/hitsicik/thebaidees.com/demo/system/Database/BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `service...', Array, false)
#4 /home1/hitsicik/thebaidees.com/demo/app/Models/FrontendModel.php(347): CodeIgniter\Database\BaseBuilder->get()
#5 /home1/hitsicik/thebaidees.com/demo/app/Controllers/Frontend.php(74): App\Models\FrontendModel->getFirstSalonServices()
#6 /home1/hitsicik/thebaidees.com/demo/system/CodeIgniter.php(936): App\Controllers\Frontend->index()
#7 /home1/hitsicik/thebaidees.com/demo/system/CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 /home1/hitsicik/thebaidees.com/demo/system/CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 /home1/hitsicik/thebaidees.com/demo/index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2021-12-28 06:45:30 --> mysqli_sql_exception: Unknown column 'gender' in 'field list' in /home1/hitsicik/thebaidees.com/demo/system/Database/MySQLi/Connection.php:314
Stack trace:
#0 /home1/hitsicik/thebaidees.com/demo/system/Database/MySQLi/Connection.php(314): mysqli->query('SELECT DISTINCT...')
#1 /home1/hitsicik/thebaidees.com/demo/system/Database/BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT DISTINCT...')
#2 /home1/hitsicik/thebaidees.com/demo/system/Database/BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT DISTINCT...')
#3 /home1/hitsicik/thebaidees.com/demo/system/Database/BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT DISTINCT...', Array, false)
#4 /home1/hitsicik/thebaidees.com/demo/app/Models/FrontendModel.php(380): CodeIgniter\Database\BaseBuilder->get()
#5 /home1/hitsicik/thebaidees.com/demo/app/Controllers/Frontend.php(77): App\Models\FrontendModel->getMaleSalonServices()
#6 /home1/hitsicik/thebaidees.com/demo/system/CodeIgniter.php(936): App\Controllers\Frontend->index()
#7 /home1/hitsicik/thebaidees.com/demo/system/CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 /home1/hitsicik/thebaidees.com/demo/system/CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 /home1/hitsicik/thebaidees.com/demo/index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2021-12-28 06:45:30 --> mysqli_sql_exception: Unknown column 'gender' in 'field list' in /home1/hitsicik/thebaidees.com/demo/system/Database/MySQLi/Connection.php:314
Stack trace:
#0 /home1/hitsicik/thebaidees.com/demo/system/Database/MySQLi/Connection.php(314): mysqli->query('SELECT DISTINCT...')
#1 /home1/hitsicik/thebaidees.com/demo/system/Database/BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT DISTINCT...')
#2 /home1/hitsicik/thebaidees.com/demo/system/Database/BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT DISTINCT...')
#3 /home1/hitsicik/thebaidees.com/demo/system/Database/BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT DISTINCT...', Array, false)
#4 /home1/hitsicik/thebaidees.com/demo/app/Models/FrontendModel.php(394): CodeIgniter\Database\BaseBuilder->get()
#5 /home1/hitsicik/thebaidees.com/demo/app/Controllers/Frontend.php(78): App\Models\FrontendModel->getFemaleSalonServices()
#6 /home1/hitsicik/thebaidees.com/demo/system/CodeIgniter.php(936): App\Controllers\Frontend->index()
#7 /home1/hitsicik/thebaidees.com/demo/system/CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 /home1/hitsicik/thebaidees.com/demo/system/CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 /home1/hitsicik/thebaidees.com/demo/index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2021-12-28 06:50:06 --> Call to a member function getFirstProdCategory() on null
#0 /home1/hitsicik/thebaidees.com/demo/system/CodeIgniter.php(936): App\Controllers\Frontend->contact()
#1 /home1/hitsicik/thebaidees.com/demo/system/CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#2 /home1/hitsicik/thebaidees.com/demo/system/CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#3 /home1/hitsicik/thebaidees.com/demo/index.php(48): CodeIgniter\CodeIgniter->run()
#4 {main}
