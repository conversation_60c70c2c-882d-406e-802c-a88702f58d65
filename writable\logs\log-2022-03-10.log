CRITICAL - 2022-03-10 00:28:06 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 00:28:06 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 00:28:06 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 00:28:06 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 00:28:06 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 00:31:01 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 00:31:01 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 00:31:02 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 00:31:02 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 00:31:02 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 00:31:24 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 00:31:24 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 00:31:24 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 00:31:24 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 00:31:24 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
ERROR - 2022-03-10 00:32:04 --> mysqli_sql_exception: Unknown column 'helps_withtags' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT DISTINCT...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT DISTINCT...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT DISTINCT...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT DISTINCT...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(185): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(134): App\Models\FrontendModel->getFilterTagsByCategory('6')
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('test50')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2022-03-10 00:32:04 --> mysqli_sql_exception: Table 'rajatmkt_db.tbl_salonservice' doesn't exist in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `service...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `service...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `service...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `service...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(462): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(137): App\Models\FrontendModel->getFirstSalonServices()
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('test50')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2022-03-10 00:32:04 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 00:32:04 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 00:32:04 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 00:32:07 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 00:32:07 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 00:32:07 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 00:32:07 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 02:07:42 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 02:07:42 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 02:07:42 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 02:07:42 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 02:07:42 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 02:07:42 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 02:07:42 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 02:07:42 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 02:07:42 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 02:07:42 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 02:07:42 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 02:08:11 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 02:08:11 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 02:08:11 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 02:08:11 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 02:08:11 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 02:08:11 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 02:08:11 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 02:08:11 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 02:08:11 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 02:08:11 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 02:08:11 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 02:08:11 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 02:12:12 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 02:12:13 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 02:12:13 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 02:15:53 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 02:15:55 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 03:59:46 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 04:17:35 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
ERROR - 2022-03-10 04:19:24 --> mysqli_sql_exception: Unknown column 'cat_banner_image' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `cat_id`...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `cat_id`...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `cat_id`...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `cat_id`...', Array, false)
#4 E:\web6\rajatmkt\app\Models\CategoryModel.php(92): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Admin\Category.php(182): App\Models\CategoryModel->getCategoryBySlug('test3', 4)
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Admin\Category->savecategory()
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Category))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2022-03-10 04:19:34 --> mysqli_sql_exception: Unknown column 'cat_banner_image' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `cat_id`...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `cat_id`...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `cat_id`...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `cat_id`...', Array, false)
#4 E:\web6\rajatmkt\app\Models\CategoryModel.php(92): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Admin\Category.php(182): App\Models\CategoryModel->getCategoryBySlug('test2', 3)
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Admin\Category->savecategory()
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Category))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2022-03-10 04:19:42 --> mysqli_sql_exception: Unknown column 'cat_banner_image' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `cat_id`...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `cat_id`...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `cat_id`...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `cat_id`...', Array, false)
#4 E:\web6\rajatmkt\app\Models\CategoryModel.php(92): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Admin\Category.php(182): App\Models\CategoryModel->getCategoryBySlug('test50', 6)
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Admin\Category->savecategory()
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Category))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2022-03-10 04:19:50 --> mysqli_sql_exception: Unknown column 'cat_banner_image' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `cat_id`...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `cat_id`...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `cat_id`...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `cat_id`...', Array, false)
#4 E:\web6\rajatmkt\app\Models\CategoryModel.php(92): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Admin\Category.php(182): App\Models\CategoryModel->getCategoryBySlug('test3', 4)
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Admin\Category->savecategory()
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Category))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2022-03-10 04:20:34 --> mysqli_sql_exception: Unknown column 'cat_banner_image' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `cat_id`...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `cat_id`...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `cat_id`...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `cat_id`...', Array, false)
#4 E:\web6\rajatmkt\app\Models\CategoryModel.php(92): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Admin\Category.php(182): App\Models\CategoryModel->getCategoryBySlug('test', 1)
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Admin\Category->savecategory()
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Category))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2022-03-10 04:27:51 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 04:28:14 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
ERROR - 2022-03-10 04:29:55 --> mysqli_sql_exception: Unknown column 'cat_banner_image' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `cat_id`...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `cat_id`...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `cat_id`...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `cat_id`...', Array, false)
#4 E:\web6\rajatmkt\app\Models\CategoryModel.php(92): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Admin\Category.php(182): App\Models\CategoryModel->getCategoryBySlug('battery-charger...', 4)
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Admin\Category->savecategory()
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Category))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2022-03-10 04:30:07 --> mysqli_sql_exception: Unknown column 'cat_banner_image' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `cat_id`...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `cat_id`...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `cat_id`...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `cat_id`...', Array, false)
#4 E:\web6\rajatmkt\app\Models\CategoryModel.php(92): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Admin\Category.php(182): App\Models\CategoryModel->getCategoryBySlug('stabilizers', 6)
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Admin\Category->savecategory()
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Category))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2022-03-10 04:30:19 --> mysqli_sql_exception: Unknown column 'cat_banner_image' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `cat_id`...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `cat_id`...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `cat_id`...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `cat_id`...', Array, false)
#4 E:\web6\rajatmkt\app\Models\CategoryModel.php(92): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Admin\Category.php(182): App\Models\CategoryModel->getCategoryBySlug('geysers', 3)
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Admin\Category->savecategory()
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Category))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2022-03-10 04:30:33 --> mysqli_sql_exception: Unknown column 'cat_banner_image' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `cat_id`...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `cat_id`...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `cat_id`...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `cat_id`...', Array, false)
#4 E:\web6\rajatmkt\app\Models\CategoryModel.php(92): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Admin\Category.php(182): App\Models\CategoryModel->getCategoryBySlug('smf-batteries', 1)
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Admin\Category->savecategory()
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Category))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2022-03-10 04:30:42 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
ERROR - 2022-03-10 04:37:17 --> mysqli_sql_exception: Unknown column 'cat_banner_image' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `cat_id`...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `cat_id`...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `cat_id`...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `cat_id`...', Array, false)
#4 E:\web6\rajatmkt\app\Models\CategoryModel.php(92): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Admin\Category.php(178): App\Models\CategoryModel->getCategoryBySlug('smf-batteries', 1)
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Admin\Category->savecategory()
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Category))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2022-03-10 04:37:25 --> mysqli_sql_exception: Unknown column 'cat_banner_image' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `cat_id`...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `cat_id`...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `cat_id`...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `cat_id`...', Array, false)
#4 E:\web6\rajatmkt\app\Models\CategoryModel.php(92): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Admin\Category.php(178): App\Models\CategoryModel->getCategoryBySlug('geysers', 3)
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Admin\Category->savecategory()
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Category))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2022-03-10 04:37:33 --> mysqli_sql_exception: Unknown column 'cat_banner_image' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `cat_id`...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `cat_id`...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `cat_id`...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `cat_id`...', Array, false)
#4 E:\web6\rajatmkt\app\Models\CategoryModel.php(92): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Admin\Category.php(178): App\Models\CategoryModel->getCategoryBySlug('stabilizers', 6)
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Admin\Category->savecategory()
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Category))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2022-03-10 04:37:40 --> mysqli_sql_exception: Unknown column 'cat_banner_image' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `cat_id`...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `cat_id`...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `cat_id`...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `cat_id`...', Array, false)
#4 E:\web6\rajatmkt\app\Models\CategoryModel.php(92): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Admin\Category.php(178): App\Models\CategoryModel->getCategoryBySlug('battery-charger...', 4)
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Admin\Category->savecategory()
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Category))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2022-03-10 04:39:26 --> mysqli_sql_exception: Unknown column 'cat_banner_image' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `cat_id`...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `cat_id`...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `cat_id`...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `cat_id`...', Array, false)
#4 E:\web6\rajatmkt\app\Models\CategoryModel.php(92): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Admin\Category.php(178): App\Models\CategoryModel->getCategoryBySlug('smf-batteries', 1)
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Admin\Category->savecategory()
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Category))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2022-03-10 04:40:24 --> mysqli_sql_exception: Unknown column 'cat_banner_image' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `cat_id`...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `cat_id`...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `cat_id`...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `cat_id`...', Array, false)
#4 E:\web6\rajatmkt\app\Models\CategoryModel.php(92): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Admin\Category.php(178): App\Models\CategoryModel->getCategoryBySlug('smf-batteries', 1)
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Admin\Category->savecategory()
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Category))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2022-03-10 04:41:04 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 04:51:06 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
ERROR - 2022-03-10 05:09:07 --> mysqli_sql_exception: Unknown column 'cat_banner_image' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `cat_id`...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `cat_id`...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `cat_id`...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `cat_id`...', Array, false)
#4 E:\web6\rajatmkt\app\Models\CategoryModel.php(92): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Admin\Category.php(178): App\Models\CategoryModel->getCategoryBySlug('smf-batteries', 1)
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Admin\Category->savecategory()
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Category))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2022-03-10 05:09:08 --> Unable to save the image. Please make sure the image and file directory are writable.
#0 E:\web6\rajatmkt\system\Images\Handlers\GDHandler.php(307): CodeIgniter\Images\Exceptions\ImageException::forSaveFailed()
#1 E:\web6\rajatmkt\app\Common.php(107): CodeIgniter\Images\Handlers\GDHandler->save('./assets/cat_im...')
#2 E:\web6\rajatmkt\app\Controllers\Admin\Category.php(259): uploadImage('./assets/cat_im...', 'catimg', 'battery1_164691...', Object(CodeIgniter\Images\Handlers\GDHandler), '150', '150')
#3 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Admin\Category->savecategory()
#4 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Category))
#5 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#6 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#7 {main}
ERROR - 2022-03-10 05:09:14 --> mysqli_sql_exception: Unknown column 'cat_banner_image' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `cat_id`...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `cat_id`...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `cat_id`...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `cat_id`...', Array, false)
#4 E:\web6\rajatmkt\app\Models\CategoryModel.php(92): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Admin\Category.php(178): App\Models\CategoryModel->getCategoryBySlug('smf-batteries', 1)
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Admin\Category->savecategory()
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Category))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2022-03-10 05:09:14 --> Unable to save the image. Please make sure the image and file directory are writable.
#0 E:\web6\rajatmkt\system\Images\Handlers\GDHandler.php(307): CodeIgniter\Images\Exceptions\ImageException::forSaveFailed()
#1 E:\web6\rajatmkt\app\Common.php(107): CodeIgniter\Images\Handlers\GDHandler->save('./assets/cat_im...')
#2 E:\web6\rajatmkt\app\Controllers\Admin\Category.php(259): uploadImage('./assets/cat_im...', 'catimg', 'battery1_164691...', Object(CodeIgniter\Images\Handlers\GDHandler), '150', '150')
#3 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Admin\Category->savecategory()
#4 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Category))
#5 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#6 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#7 {main}
CRITICAL - 2022-03-10 05:09:16 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
ERROR - 2022-03-10 05:09:22 --> mysqli_sql_exception: Unknown column 'cat_banner_image' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `cat_id`...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `cat_id`...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `cat_id`...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `cat_id`...', Array, false)
#4 E:\web6\rajatmkt\app\Models\CategoryModel.php(92): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Admin\Category.php(178): App\Models\CategoryModel->getCategoryBySlug('smf-batteries', 1)
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Admin\Category->savecategory()
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Category))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2022-03-10 05:09:22 --> Unable to save the image. Please make sure the image and file directory are writable.
#0 E:\web6\rajatmkt\system\Images\Handlers\GDHandler.php(307): CodeIgniter\Images\Exceptions\ImageException::forSaveFailed()
#1 E:\web6\rajatmkt\app\Common.php(107): CodeIgniter\Images\Handlers\GDHandler->save('./assets/cat_im...')
#2 E:\web6\rajatmkt\app\Controllers\Admin\Category.php(259): uploadImage('./assets/cat_im...', 'catimg', 'battery1_164691...', Object(CodeIgniter\Images\Handlers\GDHandler), '150', '150')
#3 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Admin\Category->savecategory()
#4 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Category))
#5 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#6 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#7 {main}
CRITICAL - 2022-03-10 05:10:16 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
ERROR - 2022-03-10 05:10:22 --> mysqli_sql_exception: Unknown column 'cat_banner_image' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `cat_id`...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `cat_id`...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `cat_id`...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `cat_id`...', Array, false)
#4 E:\web6\rajatmkt\app\Models\CategoryModel.php(92): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Admin\Category.php(178): App\Models\CategoryModel->getCategoryBySlug('smf-batteries', 1)
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Admin\Category->savecategory()
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Category))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2022-03-10 05:11:04 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
ERROR - 2022-03-10 05:11:08 --> mysqli_sql_exception: Unknown column 'cat_banner_image' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `cat_id`...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `cat_id`...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `cat_id`...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `cat_id`...', Array, false)
#4 E:\web6\rajatmkt\app\Models\CategoryModel.php(92): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Admin\Category.php(178): App\Models\CategoryModel->getCategoryBySlug('geysers', 3)
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Admin\Category->savecategory()
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Category))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2022-03-10 05:11:11 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
ERROR - 2022-03-10 05:11:16 --> mysqli_sql_exception: Unknown column 'cat_banner_image' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `cat_id`...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `cat_id`...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `cat_id`...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `cat_id`...', Array, false)
#4 E:\web6\rajatmkt\app\Models\CategoryModel.php(92): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Admin\Category.php(178): App\Models\CategoryModel->getCategoryBySlug('stabilizers', 6)
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Admin\Category->savecategory()
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Category))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2022-03-10 05:11:18 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
ERROR - 2022-03-10 05:11:23 --> mysqli_sql_exception: Unknown column 'cat_banner_image' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `cat_id`...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `cat_id`...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `cat_id`...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `cat_id`...', Array, false)
#4 E:\web6\rajatmkt\app\Models\CategoryModel.php(92): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Admin\Category.php(178): App\Models\CategoryModel->getCategoryBySlug('battery-charger...', 4)
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Admin\Category->savecategory()
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Category))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2022-03-10 05:11:30 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 05:30:35 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 05:30:35 --> Unable to save the image. Please make sure the image and file directory are writable.
#0 E:\web6\rajatmkt\system\Images\Handlers\GDHandler.php(307): CodeIgniter\Images\Exceptions\ImageException::forSaveFailed()
#1 E:\web6\rajatmkt\app\Common.php(137): CodeIgniter\Images\Handlers\GDHandler->save('./assets/brand_...', 95)
#2 E:\web6\rajatmkt\app\Controllers\Admin\Brand.php(181): uploadBrand('./assets/brand_...', 'resizeimage', 'logo5_164691183...', Object(CodeIgniter\Images\Handlers\GDHandler), '125', '35')
#3 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Admin\Brand->savebrand()
#4 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Brand))
#5 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#6 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#7 {main}
CRITICAL - 2022-03-10 05:31:29 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 05:31:29 --> Unable to save the image. Please make sure the image and file directory are writable.
#0 E:\web6\rajatmkt\system\Images\Handlers\GDHandler.php(307): CodeIgniter\Images\Exceptions\ImageException::forSaveFailed()
#1 E:\web6\rajatmkt\app\Common.php(137): CodeIgniter\Images\Handlers\GDHandler->save('./assets/brand_...', 95)
#2 E:\web6\rajatmkt\app\Controllers\Admin\Brand.php(181): uploadBrand('./assets/brand_...', 'resizeimage', 'logo5_164691188...', Object(CodeIgniter\Images\Handlers\GDHandler), '125', '35')
#3 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Admin\Brand->savebrand()
#4 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Brand))
#5 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#6 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#7 {main}
CRITICAL - 2022-03-10 05:32:36 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 05:37:39 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 05:37:40 --> Unable to save the image. Please make sure the image and file directory are writable.
#0 E:\web6\rajatmkt\system\Images\Handlers\GDHandler.php(307): CodeIgniter\Images\Exceptions\ImageException::forSaveFailed()
#1 E:\web6\rajatmkt\app\Common.php(137): CodeIgniter\Images\Handlers\GDHandler->save('./assets/brand_...', 95)
#2 E:\web6\rajatmkt\app\Controllers\Admin\Brand.php(105): uploadBrand('./assets/brand_...', 'resizeimage', 'logo3_164691226...', Object(CodeIgniter\Images\Handlers\GDHandler), '125', '35')
#3 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Admin\Brand->savebrand()
#4 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Brand))
#5 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#6 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#7 {main}
CRITICAL - 2022-03-10 05:41:23 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 05:41:24 --> Unable to save the image. Please make sure the image and file directory are writable.
#0 E:\web6\rajatmkt\system\Images\Handlers\GDHandler.php(307): CodeIgniter\Images\Exceptions\ImageException::forSaveFailed()
#1 E:\web6\rajatmkt\app\Common.php(137): CodeIgniter\Images\Handlers\GDHandler->save('./assets/brand_...', 95)
#2 E:\web6\rajatmkt\app\Controllers\Admin\Brand.php(181): uploadBrand('./assets/brand_...', 'resizeimage', 'logo3_164691248...', Object(CodeIgniter\Images\Handlers\GDHandler), '125', '35')
#3 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Admin\Brand->savebrand()
#4 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Brand))
#5 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#6 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#7 {main}
CRITICAL - 2022-03-10 05:41:37 --> Unable to save the image. Please make sure the image and file directory are writable.
#0 E:\web6\rajatmkt\system\Images\Handlers\GDHandler.php(307): CodeIgniter\Images\Exceptions\ImageException::forSaveFailed()
#1 E:\web6\rajatmkt\app\Common.php(137): CodeIgniter\Images\Handlers\GDHandler->save('./assets/brand_...', 95)
#2 E:\web6\rajatmkt\app\Controllers\Admin\Brand.php(181): uploadBrand('./assets/brand_...', 'resizeimage', 'logo3_164691249...', Object(CodeIgniter\Images\Handlers\GDHandler), '125', '35')
#3 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Admin\Brand->savebrand()
#4 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Brand))
#5 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#6 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#7 {main}
CRITICAL - 2022-03-10 05:42:17 --> Unable to save the image. Please make sure the image and file directory are writable.
#0 E:\web6\rajatmkt\system\Images\Handlers\GDHandler.php(307): CodeIgniter\Images\Exceptions\ImageException::forSaveFailed()
#1 E:\web6\rajatmkt\app\Common.php(137): CodeIgniter\Images\Handlers\GDHandler->save('./assets/brand_...', 95)
#2 E:\web6\rajatmkt\app\Controllers\Admin\Brand.php(181): uploadBrand('./assets/brand_...', 'resizeimage', 'logo3_164691253...', Object(CodeIgniter\Images\Handlers\GDHandler), '125', '35')
#3 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Admin\Brand->savebrand()
#4 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Brand))
#5 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#6 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#7 {main}
CRITICAL - 2022-03-10 05:42:33 --> Unable to save the image. Please make sure the image and file directory are writable.
#0 E:\web6\rajatmkt\system\Images\Handlers\GDHandler.php(307): CodeIgniter\Images\Exceptions\ImageException::forSaveFailed()
#1 E:\web6\rajatmkt\app\Common.php(137): CodeIgniter\Images\Handlers\GDHandler->save('./assets/brand_...', 95)
#2 E:\web6\rajatmkt\app\Controllers\Admin\Brand.php(181): uploadBrand('./assets/brand_...', 'resizeimage', 'logo3_164691255...', Object(CodeIgniter\Images\Handlers\GDHandler), '125', '35')
#3 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Admin\Brand->savebrand()
#4 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Brand))
#5 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#6 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#7 {main}
CRITICAL - 2022-03-10 05:47:25 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 05:47:36 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 05:49:15 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 05:50:22 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 05:51:32 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 05:51:38 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 05:52:36 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 05:53:27 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 05:53:32 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 05:53:54 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 05:54:00 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 05:54:27 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 05:54:27 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 05:55:12 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 05:55:12 --> Unable to save the image. Please make sure the image and file directory are writable.
#0 E:\web6\rajatmkt\system\Images\Handlers\GDHandler.php(307): CodeIgniter\Images\Exceptions\ImageException::forSaveFailed()
#1 E:\web6\rajatmkt\app\Common.php(137): CodeIgniter\Images\Handlers\GDHandler->save('./assets/brand_...', 95)
#2 E:\web6\rajatmkt\app\Controllers\Admin\Brand.php(105): uploadBrand('./assets/brand_...', 'resizeimage', 'logo4_164691331...', Object(CodeIgniter\Images\Handlers\GDHandler), '125', '35')
#3 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Admin\Brand->savebrand()
#4 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Brand))
#5 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#6 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#7 {main}
CRITICAL - 2022-03-10 05:55:45 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 05:56:08 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 05:56:08 --> Unable to save the image. Please make sure the image and file directory are writable.
#0 E:\web6\rajatmkt\system\Images\Handlers\GDHandler.php(307): CodeIgniter\Images\Exceptions\ImageException::forSaveFailed()
#1 E:\web6\rajatmkt\app\Common.php(137): CodeIgniter\Images\Handlers\GDHandler->save('./assets/brand_...', 95)
#2 E:\web6\rajatmkt\app\Controllers\Admin\Brand.php(105): uploadBrand('./assets/brand_...', 'resizeimage', 'logo7_164691336...', Object(CodeIgniter\Images\Handlers\GDHandler), '125', '35')
#3 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Admin\Brand->savebrand()
#4 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Brand))
#5 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#6 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#7 {main}
CRITICAL - 2022-03-10 05:56:55 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 05:56:55 --> Unable to save the image. Please make sure the image and file directory are writable.
#0 E:\web6\rajatmkt\system\Images\Handlers\GDHandler.php(307): CodeIgniter\Images\Exceptions\ImageException::forSaveFailed()
#1 E:\web6\rajatmkt\app\Common.php(137): CodeIgniter\Images\Handlers\GDHandler->save('./assets/brand_...', 95)
#2 E:\web6\rajatmkt\app\Controllers\Admin\Brand.php(105): uploadBrand('./assets/brand_...', 'resizeimage', 'logo2_164691341...', Object(CodeIgniter\Images\Handlers\GDHandler), '125', '35')
#3 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Admin\Brand->savebrand()
#4 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Brand))
#5 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#6 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#7 {main}
CRITICAL - 2022-03-10 05:57:50 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 05:57:50 --> Unable to save the image. Please make sure the image and file directory are writable.
#0 E:\web6\rajatmkt\system\Images\Handlers\GDHandler.php(307): CodeIgniter\Images\Exceptions\ImageException::forSaveFailed()
#1 E:\web6\rajatmkt\app\Common.php(137): CodeIgniter\Images\Handlers\GDHandler->save('./assets/brand_...', 95)
#2 E:\web6\rajatmkt\app\Controllers\Admin\Brand.php(105): uploadBrand('./assets/brand_...', 'resizeimage', 'logo6_164691347...', Object(CodeIgniter\Images\Handlers\GDHandler), '125', '35')
#3 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Admin\Brand->savebrand()
#4 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Brand))
#5 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#6 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#7 {main}
CRITICAL - 2022-03-10 05:58:33 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 05:58:34 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 05:59:04 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 05:59:04 --> Unable to save the image. Please make sure the image and file directory are writable.
#0 E:\web6\rajatmkt\system\Images\Handlers\GDHandler.php(307): CodeIgniter\Images\Exceptions\ImageException::forSaveFailed()
#1 E:\web6\rajatmkt\app\Common.php(137): CodeIgniter\Images\Handlers\GDHandler->save('./assets/brand_...', 95)
#2 E:\web6\rajatmkt\app\Controllers\Admin\Brand.php(105): uploadBrand('./assets/brand_...', 'resizeimage', 'logo1_164691354...', Object(CodeIgniter\Images\Handlers\GDHandler), '125', '35')
#3 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Admin\Brand->savebrand()
#4 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Brand))
#5 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#6 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#7 {main}
CRITICAL - 2022-03-10 05:59:52 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:02:19 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:04:02 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
ERROR - 2022-03-10 06:04:22 --> mysqli_sql_exception: Unknown column 'cat_banner_image' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `cat_id`...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `cat_id`...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `cat_id`...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `cat_id`...', Array, false)
#4 E:\web6\rajatmkt\app\Models\CategoryModel.php(92): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Admin\Category.php(84): App\Models\CategoryModel->getCategoryBySlug('office-ups')
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Admin\Category->savecategory()
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Category))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2022-03-10 06:05:15 --> mysqli_sql_exception: Unknown column 'cat_banner_image' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `cat_id`...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `cat_id`...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `cat_id`...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `cat_id`...', Array, false)
#4 E:\web6\rajatmkt\app\Models\CategoryModel.php(92): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Admin\Category.php(84): App\Models\CategoryModel->getCategoryBySlug('online-ups')
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Admin\Category->savecategory()
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Category))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2022-03-10 06:06:54 --> mysqli_sql_exception: Unknown column 'cat_banner_image' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `cat_id`...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `cat_id`...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `cat_id`...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `cat_id`...', Array, false)
#4 E:\web6\rajatmkt\app\Models\CategoryModel.php(92): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Admin\Category.php(84): App\Models\CategoryModel->getCategoryBySlug('hkva-hi-capacit...')
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Admin\Category->savecategory()
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Category))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2022-03-10 06:07:29 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
ERROR - 2022-03-10 06:07:56 --> mysqli_sql_exception: Unknown column 'cat_banner_image' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `cat_id`...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `cat_id`...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `cat_id`...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `cat_id`...', Array, false)
#4 E:\web6\rajatmkt\app\Models\CategoryModel.php(92): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Admin\Category.php(178): App\Models\CategoryModel->getCategoryBySlug('hkva-hi-capacit...', 9)
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Admin\Category->savecategory()
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Category))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2022-03-10 06:08:00 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:08:32 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:09:14 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:11:10 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:11:13 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:14:07 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
ERROR - 2022-03-10 06:14:19 --> mysqli_sql_exception: Unknown column 'helps_withtags' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT DISTINCT...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT DISTINCT...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT DISTINCT...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT DISTINCT...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(185): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(135): App\Models\FrontendModel->getFilterTagsByCategory('1')
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2022-03-10 06:14:19 --> mysqli_sql_exception: Table 'rajatmkt_db.tbl_salonservice' doesn't exist in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `service...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `service...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `service...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `service...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(462): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(138): App\Models\FrontendModel->getFirstSalonServices()
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2022-03-10 06:14:19 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:14:19 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:14:19 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:14:19 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:14:19 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:14:19 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
ERROR - 2022-03-10 06:15:50 --> mysqli_sql_exception: Unknown column 'helps_withtags' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT DISTINCT...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT DISTINCT...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT DISTINCT...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT DISTINCT...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(185): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(135): App\Models\FrontendModel->getFilterTagsByCategory('1')
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2022-03-10 06:15:50 --> mysqli_sql_exception: Table 'rajatmkt_db.tbl_salonservice' doesn't exist in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `service...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `service...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `service...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `service...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(462): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(138): App\Models\FrontendModel->getFirstSalonServices()
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2022-03-10 06:15:50 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:15:50 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:15:50 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:15:50 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:15:50 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:15:50 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
ERROR - 2022-03-10 06:20:44 --> mysqli_sql_exception: Unknown column 'helps_withtags' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT DISTINCT...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT DISTINCT...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT DISTINCT...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT DISTINCT...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(185): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(135): App\Models\FrontendModel->getFilterTagsByCategory('1')
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2022-03-10 06:20:44 --> mysqli_sql_exception: Table 'rajatmkt_db.tbl_salonservice' doesn't exist in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `service...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `service...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `service...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `service...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(462): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(138): App\Models\FrontendModel->getFirstSalonServices()
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2022-03-10 06:20:44 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:20:45 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:20:45 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:20:45 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:20:45 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
ERROR - 2022-03-10 06:40:22 --> mysqli_sql_exception: Unknown column 'helps_withtags' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT DISTINCT...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT DISTINCT...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT DISTINCT...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT DISTINCT...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(185): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(135): App\Models\FrontendModel->getFilterTagsByCategory('1')
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2022-03-10 06:40:22 --> mysqli_sql_exception: Table 'rajatmkt_db.tbl_salonservice' doesn't exist in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `service...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `service...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `service...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `service...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(462): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(138): App\Models\FrontendModel->getFirstSalonServices()
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2022-03-10 06:40:22 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:40:22 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:40:22 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:40:22 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
ERROR - 2022-03-10 06:42:43 --> mysqli_sql_exception: Unknown column 'helps_withtags' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT DISTINCT...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT DISTINCT...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT DISTINCT...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT DISTINCT...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(185): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(135): App\Models\FrontendModel->getFilterTagsByCategory('1')
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2022-03-10 06:42:43 --> mysqli_sql_exception: Table 'rajatmkt_db.tbl_salonservice' doesn't exist in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `service...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `service...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `service...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `service...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(462): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(138): App\Models\FrontendModel->getFirstSalonServices()
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2022-03-10 06:42:43 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:42:43 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:42:43 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:42:43 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
ERROR - 2022-03-10 06:44:54 --> mysqli_sql_exception: Unknown column 'helps_withtags' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT DISTINCT...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT DISTINCT...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT DISTINCT...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT DISTINCT...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(185): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(135): App\Models\FrontendModel->getFilterTagsByCategory('1')
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2022-03-10 06:44:54 --> mysqli_sql_exception: Table 'rajatmkt_db.tbl_salonservice' doesn't exist in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `service...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `service...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `service...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `service...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(462): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(138): App\Models\FrontendModel->getFirstSalonServices()
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2022-03-10 06:44:54 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:44:54 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:44:54 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
ERROR - 2022-03-10 06:45:11 --> mysqli_sql_exception: Unknown column 'helps_withtags' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT DISTINCT...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT DISTINCT...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT DISTINCT...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT DISTINCT...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(185): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(135): App\Models\FrontendModel->getFilterTagsByCategory('1')
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2022-03-10 06:45:11 --> mysqli_sql_exception: Table 'rajatmkt_db.tbl_salonservice' doesn't exist in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `service...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `service...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `service...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `service...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(462): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(138): App\Models\FrontendModel->getFirstSalonServices()
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2022-03-10 06:45:11 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:45:11 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:45:11 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:45:33 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
ERROR - 2022-03-10 06:47:01 --> mysqli_sql_exception: Unknown column 'helps_withtags' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT DISTINCT...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT DISTINCT...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT DISTINCT...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT DISTINCT...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(185): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(135): App\Models\FrontendModel->getFilterTagsByCategory('1')
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2022-03-10 06:47:01 --> mysqli_sql_exception: Table 'rajatmkt_db.tbl_salonservice' doesn't exist in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `service...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `service...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `service...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `service...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(462): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(138): App\Models\FrontendModel->getFirstSalonServices()
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2022-03-10 06:47:01 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:47:01 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:47:01 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
ERROR - 2022-03-10 06:47:02 --> mysqli_sql_exception: Unknown column 'helps_withtags' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT DISTINCT...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT DISTINCT...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT DISTINCT...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT DISTINCT...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(185): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(135): App\Models\FrontendModel->getFilterTagsByCategory('1')
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2022-03-10 06:47:02 --> mysqli_sql_exception: Table 'rajatmkt_db.tbl_salonservice' doesn't exist in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `service...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `service...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `service...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `service...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(462): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(138): App\Models\FrontendModel->getFirstSalonServices()
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2022-03-10 06:47:02 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:47:02 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:47:02 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:47:58 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
ERROR - 2022-03-10 06:48:08 --> mysqli_sql_exception: Unknown column 'helps_withtags' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT DISTINCT...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT DISTINCT...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT DISTINCT...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT DISTINCT...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(185): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(135): App\Models\FrontendModel->getFilterTagsByCategory('1')
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2022-03-10 06:48:08 --> mysqli_sql_exception: Table 'rajatmkt_db.tbl_salonservice' doesn't exist in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `service...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `service...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `service...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `service...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(462): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(138): App\Models\FrontendModel->getFirstSalonServices()
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2022-03-10 06:48:08 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:48:08 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:48:08 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
ERROR - 2022-03-10 06:49:22 --> mysqli_sql_exception: Unknown column 'helps_withtags' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT DISTINCT...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT DISTINCT...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT DISTINCT...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT DISTINCT...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(185): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(135): App\Models\FrontendModel->getFilterTagsByCategory('1')
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2022-03-10 06:49:22 --> mysqli_sql_exception: Table 'rajatmkt_db.tbl_salonservice' doesn't exist in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `service...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `service...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `service...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `service...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(462): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(138): App\Models\FrontendModel->getFirstSalonServices()
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2022-03-10 06:49:22 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:49:22 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:49:22 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
ERROR - 2022-03-10 06:49:23 --> mysqli_sql_exception: Unknown column 'helps_withtags' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT DISTINCT...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT DISTINCT...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT DISTINCT...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT DISTINCT...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(185): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(135): App\Models\FrontendModel->getFilterTagsByCategory('1')
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2022-03-10 06:49:23 --> mysqli_sql_exception: Table 'rajatmkt_db.tbl_salonservice' doesn't exist in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `service...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `service...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `service...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `service...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(462): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(138): App\Models\FrontendModel->getFirstSalonServices()
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2022-03-10 06:49:24 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:49:24 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:49:24 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
ERROR - 2022-03-10 06:49:27 --> mysqli_sql_exception: Unknown column 'helps_withtags' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT DISTINCT...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT DISTINCT...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT DISTINCT...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT DISTINCT...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(185): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(135): App\Models\FrontendModel->getFilterTagsByCategory('1')
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2022-03-10 06:49:27 --> mysqli_sql_exception: Table 'rajatmkt_db.tbl_salonservice' doesn't exist in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `service...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `service...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `service...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `service...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(462): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(138): App\Models\FrontendModel->getFirstSalonServices()
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2022-03-10 06:49:27 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:49:27 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:49:27 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
ERROR - 2022-03-10 06:49:43 --> mysqli_sql_exception: Unknown column 'helps_withtags' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT DISTINCT...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT DISTINCT...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT DISTINCT...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT DISTINCT...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(185): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(135): App\Models\FrontendModel->getFilterTagsByCategory('1')
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2022-03-10 06:49:43 --> mysqli_sql_exception: Table 'rajatmkt_db.tbl_salonservice' doesn't exist in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `service...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `service...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `service...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `service...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(462): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(138): App\Models\FrontendModel->getFirstSalonServices()
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2022-03-10 06:49:43 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:49:43 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:49:43 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
ERROR - 2022-03-10 06:49:59 --> mysqli_sql_exception: Unknown column 'helps_withtags' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT DISTINCT...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT DISTINCT...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT DISTINCT...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT DISTINCT...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(185): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(135): App\Models\FrontendModel->getFilterTagsByCategory('1')
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2022-03-10 06:49:59 --> mysqli_sql_exception: Table 'rajatmkt_db.tbl_salonservice' doesn't exist in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `service...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `service...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `service...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `service...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(462): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(138): App\Models\FrontendModel->getFirstSalonServices()
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2022-03-10 06:49:59 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:49:59 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:49:59 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
ERROR - 2022-03-10 06:51:02 --> mysqli_sql_exception: Unknown column 'helps_withtags' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT DISTINCT...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT DISTINCT...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT DISTINCT...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT DISTINCT...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(185): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(135): App\Models\FrontendModel->getFilterTagsByCategory('1')
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2022-03-10 06:51:02 --> mysqli_sql_exception: Table 'rajatmkt_db.tbl_salonservice' doesn't exist in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `service...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `service...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `service...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `service...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(462): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(138): App\Models\FrontendModel->getFirstSalonServices()
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2022-03-10 06:51:02 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:51:02 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:51:02 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
ERROR - 2022-03-10 06:51:27 --> mysqli_sql_exception: Unknown column 'helps_withtags' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT DISTINCT...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT DISTINCT...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT DISTINCT...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT DISTINCT...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(185): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(135): App\Models\FrontendModel->getFilterTagsByCategory('1')
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2022-03-10 06:51:27 --> mysqli_sql_exception: Table 'rajatmkt_db.tbl_salonservice' doesn't exist in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `service...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `service...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `service...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `service...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(462): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(138): App\Models\FrontendModel->getFirstSalonServices()
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2022-03-10 06:51:28 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:51:28 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:51:28 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
ERROR - 2022-03-10 06:51:29 --> mysqli_sql_exception: Unknown column 'helps_withtags' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT DISTINCT...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT DISTINCT...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT DISTINCT...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT DISTINCT...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(185): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(135): App\Models\FrontendModel->getFilterTagsByCategory('1')
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2022-03-10 06:51:29 --> mysqli_sql_exception: Table 'rajatmkt_db.tbl_salonservice' doesn't exist in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `service...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `service...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `service...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `service...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(462): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(138): App\Models\FrontendModel->getFirstSalonServices()
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2022-03-10 06:51:29 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:51:29 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:51:29 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
ERROR - 2022-03-10 06:51:30 --> mysqli_sql_exception: Unknown column 'helps_withtags' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT DISTINCT...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT DISTINCT...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT DISTINCT...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT DISTINCT...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(185): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(135): App\Models\FrontendModel->getFilterTagsByCategory('1')
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2022-03-10 06:51:30 --> mysqli_sql_exception: Table 'rajatmkt_db.tbl_salonservice' doesn't exist in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `service...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `service...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `service...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `service...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(462): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(138): App\Models\FrontendModel->getFirstSalonServices()
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2022-03-10 06:51:30 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:51:30 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:51:31 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
ERROR - 2022-03-10 06:51:55 --> mysqli_sql_exception: Unknown column 'helps_withtags' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT DISTINCT...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT DISTINCT...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT DISTINCT...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT DISTINCT...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(185): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(135): App\Models\FrontendModel->getFilterTagsByCategory('1')
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2022-03-10 06:51:55 --> mysqli_sql_exception: Table 'rajatmkt_db.tbl_salonservice' doesn't exist in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `service...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `service...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `service...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `service...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(462): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(138): App\Models\FrontendModel->getFirstSalonServices()
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2022-03-10 06:51:56 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:51:56 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:51:56 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
ERROR - 2022-03-10 06:51:57 --> mysqli_sql_exception: Unknown column 'helps_withtags' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT DISTINCT...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT DISTINCT...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT DISTINCT...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT DISTINCT...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(185): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(135): App\Models\FrontendModel->getFilterTagsByCategory('1')
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2022-03-10 06:51:57 --> mysqli_sql_exception: Table 'rajatmkt_db.tbl_salonservice' doesn't exist in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `service...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `service...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `service...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `service...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(462): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(138): App\Models\FrontendModel->getFirstSalonServices()
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2022-03-10 06:51:57 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:51:57 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:51:57 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
ERROR - 2022-03-10 06:51:59 --> mysqli_sql_exception: Unknown column 'helps_withtags' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT DISTINCT...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT DISTINCT...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT DISTINCT...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT DISTINCT...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(185): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(135): App\Models\FrontendModel->getFilterTagsByCategory('1')
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2022-03-10 06:51:59 --> mysqli_sql_exception: Table 'rajatmkt_db.tbl_salonservice' doesn't exist in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `service...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `service...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `service...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `service...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(462): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(138): App\Models\FrontendModel->getFirstSalonServices()
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2022-03-10 06:51:59 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:51:59 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:51:59 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
ERROR - 2022-03-10 06:53:39 --> mysqli_sql_exception: Unknown column 'helps_withtags' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT DISTINCT...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT DISTINCT...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT DISTINCT...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT DISTINCT...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(185): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(135): App\Models\FrontendModel->getFilterTagsByCategory('1')
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2022-03-10 06:53:39 --> mysqli_sql_exception: Table 'rajatmkt_db.tbl_salonservice' doesn't exist in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `service...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `service...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `service...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `service...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(462): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(138): App\Models\FrontendModel->getFirstSalonServices()
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2022-03-10 06:53:39 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:53:39 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:53:39 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
ERROR - 2022-03-10 06:53:40 --> mysqli_sql_exception: Unknown column 'helps_withtags' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT DISTINCT...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT DISTINCT...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT DISTINCT...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT DISTINCT...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(185): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(135): App\Models\FrontendModel->getFilterTagsByCategory('1')
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2022-03-10 06:53:40 --> mysqli_sql_exception: Table 'rajatmkt_db.tbl_salonservice' doesn't exist in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `service...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `service...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `service...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `service...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(462): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(138): App\Models\FrontendModel->getFirstSalonServices()
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2022-03-10 06:53:40 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:53:40 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:53:40 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
ERROR - 2022-03-10 06:54:11 --> mysqli_sql_exception: Unknown column 'helps_withtags' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT DISTINCT...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT DISTINCT...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT DISTINCT...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT DISTINCT...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(185): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(135): App\Models\FrontendModel->getFilterTagsByCategory('1')
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2022-03-10 06:54:11 --> mysqli_sql_exception: Table 'rajatmkt_db.tbl_salonservice' doesn't exist in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `service...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `service...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `service...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `service...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(462): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(138): App\Models\FrontendModel->getFirstSalonServices()
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2022-03-10 06:54:11 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:54:11 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:54:11 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:54:28 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
ERROR - 2022-03-10 06:55:20 --> mysqli_sql_exception: Unknown column 'helps_withtags' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT DISTINCT...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT DISTINCT...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT DISTINCT...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT DISTINCT...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(185): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(135): App\Models\FrontendModel->getFilterTagsByCategory('1')
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2022-03-10 06:55:20 --> mysqli_sql_exception: Table 'rajatmkt_db.tbl_salonservice' doesn't exist in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `service...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `service...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `service...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `service...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(462): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(138): App\Models\FrontendModel->getFirstSalonServices()
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2022-03-10 06:55:20 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:55:20 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:55:20 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:55:20 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:55:20 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
ERROR - 2022-03-10 06:55:21 --> mysqli_sql_exception: Unknown column 'helps_withtags' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT DISTINCT...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT DISTINCT...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT DISTINCT...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT DISTINCT...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(185): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(135): App\Models\FrontendModel->getFilterTagsByCategory('1')
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2022-03-10 06:55:21 --> mysqli_sql_exception: Table 'rajatmkt_db.tbl_salonservice' doesn't exist in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `service...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `service...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `service...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `service...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(462): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(138): App\Models\FrontendModel->getFirstSalonServices()
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2022-03-10 06:55:21 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:55:21 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:55:21 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:55:21 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:55:21 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
ERROR - 2022-03-10 06:55:23 --> mysqli_sql_exception: Unknown column 'helps_withtags' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT DISTINCT...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT DISTINCT...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT DISTINCT...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT DISTINCT...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(185): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(135): App\Models\FrontendModel->getFilterTagsByCategory('1')
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2022-03-10 06:55:23 --> mysqli_sql_exception: Table 'rajatmkt_db.tbl_salonservice' doesn't exist in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `service...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `service...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `service...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `service...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(462): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(138): App\Models\FrontendModel->getFirstSalonServices()
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2022-03-10 06:55:23 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:55:23 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:55:23 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:55:34 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
ERROR - 2022-03-10 06:57:00 --> mysqli_sql_exception: Unknown column 'helps_withtags' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT DISTINCT...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT DISTINCT...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT DISTINCT...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT DISTINCT...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(185): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(135): App\Models\FrontendModel->getFilterTagsByCategory('1')
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2022-03-10 06:57:00 --> mysqli_sql_exception: Table 'rajatmkt_db.tbl_salonservice' doesn't exist in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `service...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `service...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `service...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `service...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(462): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(138): App\Models\FrontendModel->getFirstSalonServices()
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2022-03-10 06:57:00 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:57:00 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:57:00 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:57:20 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
ERROR - 2022-03-10 06:58:29 --> mysqli_sql_exception: Unknown column 'helps_withtags' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT DISTINCT...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT DISTINCT...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT DISTINCT...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT DISTINCT...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(185): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(135): App\Models\FrontendModel->getFilterTagsByCategory('1')
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2022-03-10 06:58:29 --> mysqli_sql_exception: Table 'rajatmkt_db.tbl_salonservice' doesn't exist in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `service...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `service...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `service...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `service...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(462): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(138): App\Models\FrontendModel->getFirstSalonServices()
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2022-03-10 06:58:30 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:58:30 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:58:30 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:58:30 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:58:30 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
ERROR - 2022-03-10 06:58:33 --> mysqli_sql_exception: Unknown column 'helps_withtags' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT DISTINCT...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT DISTINCT...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT DISTINCT...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT DISTINCT...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(185): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(135): App\Models\FrontendModel->getFilterTagsByCategory('1')
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2022-03-10 06:58:33 --> mysqli_sql_exception: Table 'rajatmkt_db.tbl_salonservice' doesn't exist in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `service...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `service...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `service...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `service...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(462): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(138): App\Models\FrontendModel->getFirstSalonServices()
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2022-03-10 06:58:33 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:58:33 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:58:33 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:58:53 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
ERROR - 2022-03-10 06:59:08 --> mysqli_sql_exception: Unknown column 'helps_withtags' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT DISTINCT...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT DISTINCT...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT DISTINCT...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT DISTINCT...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(185): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(135): App\Models\FrontendModel->getFilterTagsByCategory('1')
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries', 'aab3238922bcc25...')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2022-03-10 06:59:08 --> mysqli_sql_exception: Table 'rajatmkt_db.tbl_salonservice' doesn't exist in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `service...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `service...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `service...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `service...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(462): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(138): App\Models\FrontendModel->getFirstSalonServices()
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries', 'aab3238922bcc25...')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2022-03-10 06:59:08 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:59:08 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:59:08 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:59:08 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 06:59:08 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
ERROR - 2022-03-10 07:01:53 --> mysqli_sql_exception: Unknown column 'helps_withtags' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT DISTINCT...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT DISTINCT...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT DISTINCT...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT DISTINCT...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(185): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(135): App\Models\FrontendModel->getFilterTagsByCategory('1')
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries', 'aab3238922bcc25...')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2022-03-10 07:01:53 --> mysqli_sql_exception: Table 'rajatmkt_db.tbl_salonservice' doesn't exist in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `service...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `service...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `service...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `service...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(462): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(138): App\Models\FrontendModel->getFirstSalonServices()
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries', 'aab3238922bcc25...')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2022-03-10 07:01:53 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 07:01:53 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 07:01:53 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 07:01:53 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 07:01:53 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 07:02:06 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
ERROR - 2022-03-10 07:02:34 --> mysqli_sql_exception: Unknown column 'helps_withtags' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT DISTINCT...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT DISTINCT...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT DISTINCT...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT DISTINCT...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(185): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(135): App\Models\FrontendModel->getFilterTagsByCategory('3')
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('geysers')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2022-03-10 07:02:34 --> mysqli_sql_exception: Table 'rajatmkt_db.tbl_salonservice' doesn't exist in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `service...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `service...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `service...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `service...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(462): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(138): App\Models\FrontendModel->getFirstSalonServices()
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('geysers')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2022-03-10 07:02:34 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 07:02:34 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 07:02:34 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
ERROR - 2022-03-10 07:02:38 --> mysqli_sql_exception: Unknown column 'helps_withtags' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT DISTINCT...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT DISTINCT...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT DISTINCT...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT DISTINCT...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(185): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(135): App\Models\FrontendModel->getFilterTagsByCategory('3')
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('geysers', 'c51ce410c124a10...')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2022-03-10 07:02:38 --> mysqli_sql_exception: Table 'rajatmkt_db.tbl_salonservice' doesn't exist in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `service...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `service...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `service...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `service...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(462): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(138): App\Models\FrontendModel->getFirstSalonServices()
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('geysers', 'c51ce410c124a10...')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2022-03-10 07:02:38 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 07:02:38 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 07:02:38 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 07:02:47 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
ERROR - 2022-03-10 07:02:50 --> mysqli_sql_exception: Unknown column 'helps_withtags' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT DISTINCT...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT DISTINCT...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT DISTINCT...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT DISTINCT...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(185): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(135): App\Models\FrontendModel->getFilterTagsByCategory('1')
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2022-03-10 07:02:50 --> mysqli_sql_exception: Table 'rajatmkt_db.tbl_salonservice' doesn't exist in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `service...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `service...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `service...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `service...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(462): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(138): App\Models\FrontendModel->getFirstSalonServices()
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2022-03-10 07:02:50 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 07:02:50 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 07:02:50 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
ERROR - 2022-03-10 07:03:57 --> mysqli_sql_exception: Unknown column 'helps_withtags' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT DISTINCT...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT DISTINCT...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT DISTINCT...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT DISTINCT...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(185): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(135): App\Models\FrontendModel->getFilterTagsByCategory('1')
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2022-03-10 07:03:57 --> mysqli_sql_exception: Table 'rajatmkt_db.tbl_salonservice' doesn't exist in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `service...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `service...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `service...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `service...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(462): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(138): App\Models\FrontendModel->getFirstSalonServices()
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2022-03-10 07:03:57 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 07:03:57 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 07:03:57 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
ERROR - 2022-03-10 07:05:17 --> mysqli_sql_exception: Unknown column 'helps_withtags' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT DISTINCT...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT DISTINCT...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT DISTINCT...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT DISTINCT...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(185): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(135): App\Models\FrontendModel->getFilterTagsByCategory('1')
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2022-03-10 07:05:17 --> mysqli_sql_exception: Table 'rajatmkt_db.tbl_salonservice' doesn't exist in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `service...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `service...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `service...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `service...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(462): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(138): App\Models\FrontendModel->getFirstSalonServices()
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2022-03-10 07:05:17 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 07:05:17 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 07:05:17 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
ERROR - 2022-03-10 07:05:18 --> mysqli_sql_exception: Unknown column 'helps_withtags' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT DISTINCT...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT DISTINCT...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT DISTINCT...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT DISTINCT...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(185): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(135): App\Models\FrontendModel->getFilterTagsByCategory('1')
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2022-03-10 07:05:18 --> mysqli_sql_exception: Table 'rajatmkt_db.tbl_salonservice' doesn't exist in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `service...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `service...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `service...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `service...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(462): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(138): App\Models\FrontendModel->getFirstSalonServices()
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2022-03-10 07:05:18 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 07:05:18 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 07:05:18 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
ERROR - 2022-03-10 07:05:48 --> mysqli_sql_exception: Unknown column 'helps_withtags' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT DISTINCT...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT DISTINCT...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT DISTINCT...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT DISTINCT...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(185): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(135): App\Models\FrontendModel->getFilterTagsByCategory('1')
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2022-03-10 07:05:48 --> mysqli_sql_exception: Table 'rajatmkt_db.tbl_salonservice' doesn't exist in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `service...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `service...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `service...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `service...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(462): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(138): App\Models\FrontendModel->getFirstSalonServices()
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2022-03-10 07:05:48 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 07:05:48 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 07:05:48 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
ERROR - 2022-03-10 07:05:50 --> mysqli_sql_exception: Unknown column 'helps_withtags' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT DISTINCT...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT DISTINCT...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT DISTINCT...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT DISTINCT...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(185): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(135): App\Models\FrontendModel->getFilterTagsByCategory('1')
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2022-03-10 07:05:50 --> mysqli_sql_exception: Table 'rajatmkt_db.tbl_salonservice' doesn't exist in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `service...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `service...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `service...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `service...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(462): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(138): App\Models\FrontendModel->getFirstSalonServices()
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2022-03-10 07:05:50 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 07:05:50 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 07:05:50 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
ERROR - 2022-03-10 07:06:06 --> mysqli_sql_exception: Unknown column 'helps_withtags' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT DISTINCT...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT DISTINCT...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT DISTINCT...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT DISTINCT...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(185): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(135): App\Models\FrontendModel->getFilterTagsByCategory('1')
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2022-03-10 07:06:06 --> mysqli_sql_exception: Table 'rajatmkt_db.tbl_salonservice' doesn't exist in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `service...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `service...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `service...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `service...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(462): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(138): App\Models\FrontendModel->getFirstSalonServices()
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2022-03-10 07:06:07 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 07:06:07 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 07:06:07 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
ERROR - 2022-03-10 07:06:22 --> mysqli_sql_exception: Unknown column 'helps_withtags' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT DISTINCT...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT DISTINCT...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT DISTINCT...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT DISTINCT...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(185): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(135): App\Models\FrontendModel->getFilterTagsByCategory('1')
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2022-03-10 07:06:22 --> mysqli_sql_exception: Table 'rajatmkt_db.tbl_salonservice' doesn't exist in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `service...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `service...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `service...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `service...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(462): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(138): App\Models\FrontendModel->getFirstSalonServices()
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2022-03-10 07:06:22 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 07:06:22 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 07:06:22 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
ERROR - 2022-03-10 07:07:05 --> mysqli_sql_exception: Unknown column 'helps_withtags' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT DISTINCT...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT DISTINCT...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT DISTINCT...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT DISTINCT...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(185): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(135): App\Models\FrontendModel->getFilterTagsByCategory('1')
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2022-03-10 07:07:05 --> mysqli_sql_exception: Table 'rajatmkt_db.tbl_salonservice' doesn't exist in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `service...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `service...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `service...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `service...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(462): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(138): App\Models\FrontendModel->getFirstSalonServices()
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2022-03-10 07:07:05 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 07:07:05 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 07:07:05 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
ERROR - 2022-03-10 07:07:20 --> mysqli_sql_exception: Unknown column 'helps_withtags' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT DISTINCT...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT DISTINCT...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT DISTINCT...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT DISTINCT...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(185): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(135): App\Models\FrontendModel->getFilterTagsByCategory('1')
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries', 'aab3238922bcc25...')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2022-03-10 07:07:20 --> mysqli_sql_exception: Table 'rajatmkt_db.tbl_salonservice' doesn't exist in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `service...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `service...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `service...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `service...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(462): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(138): App\Models\FrontendModel->getFirstSalonServices()
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries', 'aab3238922bcc25...')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2022-03-10 07:07:20 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 07:07:20 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 07:07:20 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
ERROR - 2022-03-10 07:07:27 --> mysqli_sql_exception: Unknown column 'helps_withtags' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT DISTINCT...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT DISTINCT...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT DISTINCT...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT DISTINCT...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(185): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(135): App\Models\FrontendModel->getFilterTagsByCategory('1')
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries', 'c74d97b01eae257...')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2022-03-10 07:07:27 --> mysqli_sql_exception: Table 'rajatmkt_db.tbl_salonservice' doesn't exist in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `service...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `service...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `service...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `service...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(462): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(138): App\Models\FrontendModel->getFirstSalonServices()
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries', 'c74d97b01eae257...')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2022-03-10 07:07:27 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 07:07:27 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 07:07:27 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
ERROR - 2022-03-10 07:07:29 --> mysqli_sql_exception: Unknown column 'helps_withtags' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT DISTINCT...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT DISTINCT...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT DISTINCT...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT DISTINCT...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(185): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(135): App\Models\FrontendModel->getFilterTagsByCategory('1')
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries', 'aab3238922bcc25...')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2022-03-10 07:07:29 --> mysqli_sql_exception: Table 'rajatmkt_db.tbl_salonservice' doesn't exist in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `service...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `service...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `service...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `service...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(462): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(138): App\Models\FrontendModel->getFirstSalonServices()
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries', 'aab3238922bcc25...')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2022-03-10 07:07:29 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 07:07:29 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 07:07:29 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
ERROR - 2022-03-10 07:07:37 --> mysqli_sql_exception: Unknown column 'helps_withtags' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT DISTINCT...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT DISTINCT...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT DISTINCT...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT DISTINCT...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(185): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(135): App\Models\FrontendModel->getFilterTagsByCategory('1')
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries', 'c74d97b01eae257...')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2022-03-10 07:07:37 --> mysqli_sql_exception: Table 'rajatmkt_db.tbl_salonservice' doesn't exist in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `service...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `service...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `service...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `service...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(462): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(138): App\Models\FrontendModel->getFirstSalonServices()
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries', 'c74d97b01eae257...')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2022-03-10 07:07:38 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 07:07:38 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 07:07:38 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 07:07:44 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
ERROR - 2022-03-10 07:07:47 --> mysqli_sql_exception: Unknown column 'helps_withtags' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT DISTINCT...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT DISTINCT...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT DISTINCT...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT DISTINCT...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(185): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(135): App\Models\FrontendModel->getFilterTagsByCategory('1')
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2022-03-10 07:07:47 --> mysqli_sql_exception: Table 'rajatmkt_db.tbl_salonservice' doesn't exist in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `service...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `service...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `service...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `service...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(462): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(138): App\Models\FrontendModel->getFirstSalonServices()
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2022-03-10 07:07:47 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 07:07:47 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 07:07:47 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
ERROR - 2022-03-10 07:10:27 --> mysqli_sql_exception: Unknown column 'helps_withtags' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT DISTINCT...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT DISTINCT...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT DISTINCT...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT DISTINCT...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(185): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(135): App\Models\FrontendModel->getFilterTagsByCategory('1')
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2022-03-10 07:10:27 --> mysqli_sql_exception: Table 'rajatmkt_db.tbl_salonservice' doesn't exist in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `service...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `service...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `service...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `service...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(462): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(138): App\Models\FrontendModel->getFirstSalonServices()
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2022-03-10 07:10:27 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 07:10:27 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 07:10:27 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 07:10:31 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 07:10:35 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 07:10:37 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 07:11:15 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 07:12:34 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 07:12:50 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 07:12:51 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 07:13:14 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 07:13:15 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 07:13:36 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 07:13:40 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 07:14:41 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
ERROR - 2022-03-10 07:14:49 --> mysqli_sql_exception: Unknown column 'helps_withtags' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT DISTINCT...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT DISTINCT...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT DISTINCT...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT DISTINCT...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(185): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(135): App\Models\FrontendModel->getFilterTagsByCategory('1')
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2022-03-10 07:14:49 --> mysqli_sql_exception: Table 'rajatmkt_db.tbl_salonservice' doesn't exist in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `service...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `service...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `service...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `service...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(462): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(138): App\Models\FrontendModel->getFirstSalonServices()
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2022-03-10 07:14:49 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 07:14:49 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 07:14:49 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
ERROR - 2022-03-10 07:15:20 --> mysqli_sql_exception: Unknown column 'helps_withtags' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT DISTINCT...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT DISTINCT...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT DISTINCT...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT DISTINCT...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(185): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(136): App\Models\FrontendModel->getFilterTagsByCategory('1')
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2022-03-10 07:15:20 --> mysqli_sql_exception: Table 'rajatmkt_db.tbl_salonservice' doesn't exist in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `service...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `service...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `service...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `service...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(462): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(139): App\Models\FrontendModel->getFirstSalonServices()
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2022-03-10 07:15:20 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 07:15:20 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 07:15:20 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
ERROR - 2022-03-10 07:15:25 --> mysqli_sql_exception: Unknown column 'helps_withtags' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT DISTINCT...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT DISTINCT...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT DISTINCT...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT DISTINCT...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(185): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(136): App\Models\FrontendModel->getFilterTagsByCategory('1')
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2022-03-10 07:15:25 --> mysqli_sql_exception: Table 'rajatmkt_db.tbl_salonservice' doesn't exist in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `service...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `service...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `service...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `service...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(462): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(139): App\Models\FrontendModel->getFirstSalonServices()
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2022-03-10 07:15:25 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 07:15:25 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 07:15:25 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
ERROR - 2022-03-10 07:16:20 --> mysqli_sql_exception: Unknown column 'helps_withtags' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT DISTINCT...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT DISTINCT...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT DISTINCT...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT DISTINCT...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(185): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(136): App\Models\FrontendModel->getFilterTagsByCategory('1')
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2022-03-10 07:16:20 --> mysqli_sql_exception: Table 'rajatmkt_db.tbl_salonservice' doesn't exist in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `service...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `service...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `service...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `service...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(462): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(139): App\Models\FrontendModel->getFirstSalonServices()
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2022-03-10 07:16:20 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 07:16:21 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 07:16:21 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 07:16:24 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
ERROR - 2022-03-10 07:18:13 --> mysqli_sql_exception: Unknown column 'helps_withtags' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT DISTINCT...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT DISTINCT...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT DISTINCT...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT DISTINCT...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(185): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(136): App\Models\FrontendModel->getFilterTagsByCategory('1')
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2022-03-10 07:18:13 --> mysqli_sql_exception: Table 'rajatmkt_db.tbl_salonservice' doesn't exist in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `service...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `service...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `service...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `service...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(462): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(139): App\Models\FrontendModel->getFirstSalonServices()
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2022-03-10 07:18:14 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 07:18:14 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 07:18:14 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
ERROR - 2022-03-10 07:18:17 --> mysqli_sql_exception: Unknown column 'helps_withtags' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT DISTINCT...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT DISTINCT...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT DISTINCT...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT DISTINCT...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(185): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(136): App\Models\FrontendModel->getFilterTagsByCategory('1')
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2022-03-10 07:18:17 --> mysqli_sql_exception: Table 'rajatmkt_db.tbl_salonservice' doesn't exist in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `service...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `service...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `service...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `service...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(462): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(139): App\Models\FrontendModel->getFirstSalonServices()
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2022-03-10 07:18:17 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 07:18:17 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 07:18:17 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
ERROR - 2022-03-10 07:20:26 --> mysqli_sql_exception: Unknown column 'helps_withtags' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT DISTINCT...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT DISTINCT...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT DISTINCT...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT DISTINCT...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(185): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(138): App\Models\FrontendModel->getFilterTagsByCategory('1')
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2022-03-10 07:20:26 --> mysqli_sql_exception: Table 'rajatmkt_db.tbl_salonservice' doesn't exist in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `service...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `service...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `service...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `service...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(462): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(141): App\Models\FrontendModel->getFirstSalonServices()
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2022-03-10 07:20:29 --> mysqli_sql_exception: Unknown column 'helps_withtags' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT DISTINCT...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT DISTINCT...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT DISTINCT...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT DISTINCT...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(185): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(138): App\Models\FrontendModel->getFilterTagsByCategory('1')
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2022-03-10 07:20:29 --> mysqli_sql_exception: Table 'rajatmkt_db.tbl_salonservice' doesn't exist in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `service...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `service...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `service...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `service...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(462): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(141): App\Models\FrontendModel->getFirstSalonServices()
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2022-03-10 22:52:10 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
ERROR - 2022-03-10 22:52:36 --> mysqli_sql_exception: Unknown column 'helps_withtags' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT DISTINCT...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT DISTINCT...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT DISTINCT...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT DISTINCT...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(185): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(136): App\Models\FrontendModel->getFilterTagsByCategory('1')
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2022-03-10 22:52:36 --> mysqli_sql_exception: Table 'rajatmkt_db.tbl_salonservice' doesn't exist in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `service...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `service...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `service...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `service...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(462): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(139): App\Models\FrontendModel->getFirstSalonServices()
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2022-03-10 22:52:36 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 22:52:36 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 22:52:36 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 22:52:41 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
ERROR - 2022-03-10 22:59:16 --> mysqli_sql_exception: Unknown column 'helps_withtags' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT DISTINCT...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT DISTINCT...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT DISTINCT...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT DISTINCT...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(185): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(136): App\Models\FrontendModel->getFilterTagsByCategory('1')
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2022-03-10 22:59:16 --> mysqli_sql_exception: Table 'rajatmkt_db.tbl_salonservice' doesn't exist in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `service...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `service...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `service...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `service...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(462): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(139): App\Models\FrontendModel->getFirstSalonServices()
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2022-03-10 22:59:16 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 22:59:16 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 22:59:16 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 22:59:24 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
ERROR - 2022-03-10 22:59:41 --> mysqli_sql_exception: Unknown column 'helps_withtags' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT DISTINCT...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT DISTINCT...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT DISTINCT...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT DISTINCT...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(185): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(136): App\Models\FrontendModel->getFilterTagsByCategory('1')
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2022-03-10 22:59:41 --> mysqli_sql_exception: Table 'rajatmkt_db.tbl_salonservice' doesn't exist in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `service...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `service...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `service...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `service...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(462): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(139): App\Models\FrontendModel->getFirstSalonServices()
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2022-03-10 22:59:41 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 22:59:41 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 22:59:41 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 22:59:44 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
ERROR - 2022-03-10 23:00:48 --> mysqli_sql_exception: Unknown column 'helps_withtags' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT DISTINCT...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT DISTINCT...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT DISTINCT...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT DISTINCT...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(185): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(136): App\Models\FrontendModel->getFilterTagsByCategory('1')
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries', 'c74d97b01eae257...')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2022-03-10 23:00:48 --> mysqli_sql_exception: Table 'rajatmkt_db.tbl_salonservice' doesn't exist in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `service...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `service...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `service...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `service...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(462): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(139): App\Models\FrontendModel->getFirstSalonServices()
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries', 'c74d97b01eae257...')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2022-03-10 23:00:48 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 23:00:48 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 23:00:48 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
ERROR - 2022-03-10 23:00:50 --> mysqli_sql_exception: Unknown column 'helps_withtags' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT DISTINCT...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT DISTINCT...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT DISTINCT...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT DISTINCT...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(185): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(136): App\Models\FrontendModel->getFilterTagsByCategory('1')
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries', 'aab3238922bcc25...')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2022-03-10 23:00:50 --> mysqli_sql_exception: Table 'rajatmkt_db.tbl_salonservice' doesn't exist in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `service...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `service...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `service...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `service...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(462): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(139): App\Models\FrontendModel->getFirstSalonServices()
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries', 'aab3238922bcc25...')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2022-03-10 23:00:50 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 23:00:50 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 23:00:50 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 23:00:50 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
ERROR - 2022-03-10 23:00:54 --> mysqli_sql_exception: Unknown column 'helps_withtags' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT DISTINCT...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT DISTINCT...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT DISTINCT...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT DISTINCT...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(185): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(136): App\Models\FrontendModel->getFilterTagsByCategory('1')
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries', 'aab3238922bcc25...')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2022-03-10 23:00:54 --> mysqli_sql_exception: Table 'rajatmkt_db.tbl_salonservice' doesn't exist in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `service...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `service...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `service...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `service...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(462): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(139): App\Models\FrontendModel->getFirstSalonServices()
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries', 'aab3238922bcc25...')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2022-03-10 23:00:54 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 23:00:54 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 23:00:54 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
ERROR - 2022-03-10 23:00:57 --> mysqli_sql_exception: Unknown column 'helps_withtags' in 'field list' in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT DISTINCT...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT DISTINCT...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT DISTINCT...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT DISTINCT...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(185): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(136): App\Models\FrontendModel->getFilterTagsByCategory('1')
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2022-03-10 23:00:57 --> mysqli_sql_exception: Table 'rajatmkt_db.tbl_salonservice' doesn't exist in E:\web6\rajatmkt\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\web6\rajatmkt\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `service...')
#1 E:\web6\rajatmkt\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `service...')
#2 E:\web6\rajatmkt\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `service...')
#3 E:\web6\rajatmkt\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `service...', Array, false)
#4 E:\web6\rajatmkt\app\Models\FrontendModel.php(462): CodeIgniter\Database\BaseBuilder->get()
#5 E:\web6\rajatmkt\app\Controllers\Frontend.php(139): App\Models\FrontendModel->getFirstSalonServices()
#6 E:\web6\rajatmkt\system\CodeIgniter.php(936): App\Controllers\Frontend->category('smf-batteries')
#7 E:\web6\rajatmkt\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\rajatmkt\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2022-03-10 23:00:58 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 23:00:58 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
CRITICAL - 2022-03-10 23:00:58 --> Call to undefined method App\Controllers\Frontend::_404()
#0 E:\web6\rajatmkt\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#1 E:\web6\rajatmkt\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\web6\rajatmkt\index.php(48): CodeIgniter\CodeIgniter->run()
#3 {main}
