CRITICAL - 2021-12-13 01:34:49 --> ini_set(): Headers already sent. You cannot change the session module's ini settings at this time
#0 [internal function]: CodeIgniter\Debug\Exceptions->errorHandler(2, 'ini_set(): Head...', 'E:\\xampp\\htdocs...', 80, Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\system\Session\Handlers\FileHandler.php(80): ini_set('session.save_pa...', 'E:\\xampp\\htdocs...')
#2 E:\xampp\htdocs\PROJECTS\baidees\system\Config\Services.php(706): CodeIgniter\Session\Handlers\FileHandler->__construct(Object(Config\App), '192.168.0.84')
#3 E:\xampp\htdocs\PROJECTS\baidees\system\Config\BaseService.php(100): CodeIgniter\Config\Services::session(Object(Config\App), false)
#4 E:\xampp\htdocs\PROJECTS\baidees\system\Config\Services.php(699): CodeIgniter\Config\BaseService::getSharedInstance('session', NULL)
#5 E:\xampp\htdocs\PROJECTS\baidees\system\Config\BaseService.php(171): CodeIgniter\Config\Services::session()
#6 E:\xampp\htdocs\PROJECTS\baidees\system\Common.php(999): CodeIgniter\Config\BaseService::__callStatic('session', Array)
#7 E:\xampp\htdocs\PROJECTS\baidees\app\Common.php(19): session()
#8 E:\xampp\htdocs\PROJECTS\baidees\app\Views\admin\order\orderlist.php(201): putCSRFToken(2, 'productFrm2')
#9 E:\xampp\htdocs\PROJECTS\baidees\system\View\View.php(220): include('E:\\xampp\\htdocs...')
#10 E:\xampp\htdocs\PROJECTS\baidees\system\View\View.php(222): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#11 E:\xampp\htdocs\PROJECTS\baidees\system\Common.php(1217): CodeIgniter\View\View->render('admin/order/ord...', Array, true)
#12 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Admin\Order.php(14): view('admin/order/ord...', Array)
#13 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Order->index()
#14 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Order))
#15 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#16 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#17 {main}
CRITICAL - 2021-12-13 01:34:49 --> Uncaught ErrorException: Cannot modify header information - headers already sent by (output started at E:\xampp\htdocs\PROJECTS\baidees\app\Config\Events.php:35) in E:\xampp\htdocs\PROJECTS\baidees\system\Debug\Exceptions.php:137
Stack trace:
#0 [internal function]: CodeIgniter\Debug\Exceptions->errorHandler(2, 'Cannot modify h...', 'E:\\xampp\\htdocs...', 137, Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\system\Debug\Exceptions.php(137): header('HTTP/1.1 500 In...', true, 500)
#2 [internal function]: CodeIgniter\Debug\Exceptions->exceptionHandler(Object(ErrorException))
#3 {main}
  thrown
#0 [internal function]: CodeIgniter\Debug\Exceptions->shutdownHandler()
#1 {main}
CRITICAL - 2021-12-13 04:38:29 --> Class 'App\Controllers\Frontend' not found
#0 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(969): CodeIgniter\CodeIgniter->createController()
#1 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#2 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#3 {main}
ERROR - 2021-12-13 04:40:46 --> mysqli_sql_exception: Column 'mobile' cannot be null in E:\xampp\htdocs\PROJECTS\baidees\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\xampp\htdocs\PROJECTS\baidees\system\Database\MySQLi\Connection.php(314): mysqli->query('INSERT INTO `tb...')
#1 E:\xampp\htdocs\PROJECTS\baidees\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `tb...')
#2 E:\xampp\htdocs\PROJECTS\baidees\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `tb...')
#3 E:\xampp\htdocs\PROJECTS\baidees\system\Database\BaseBuilder.php(2263): CodeIgniter\Database\BaseConnection->query('INSERT INTO `tb...', Array, false)
#4 E:\xampp\htdocs\PROJECTS\baidees\app\Models\FrontendModel.php(234): CodeIgniter\Database\BaseBuilder->insert(Array)
#5 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Frontend.php(580): App\Models\FrontendModel->insertCustomerDetails(Array)
#6 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Frontend->enterotp()
#7 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2021-12-13 07:06:00 --> mysqli_sql_exception: Unknown column 'salonservice_name' in 'field list' in E:\xampp\htdocs\PROJECTS\baidees\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\xampp\htdocs\PROJECTS\baidees\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `service...')
#1 E:\xampp\htdocs\PROJECTS\baidees\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `service...')
#2 E:\xampp\htdocs\PROJECTS\baidees\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `service...')
#3 E:\xampp\htdocs\PROJECTS\baidees\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `service...', Array, false)
#4 E:\xampp\htdocs\PROJECTS\baidees\app\Models\SalonserviceModel.php(27): CodeIgniter\Database\BaseBuilder->get()
#5 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Admin\Salonservice.php(12): App\Models\SalonserviceModel->getSalonserviceList()
#6 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Salonservice->index()
#7 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Salonservice))
#8 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2021-12-13 22:56:27 --> Error connecting to the database: No connection could be made because the target machine actively refused it.

CRITICAL - 2021-12-13 22:56:27 --> Unable to connect to the database.
#0 E:\xampp\htdocs\PROJECTS\baidees\system\Database\BaseConnection.php(618): CodeIgniter\Database\BaseConnection->initialize()
#1 E:\xampp\htdocs\PROJECTS\baidees\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `banner_...', Array, false)
#2 E:\xampp\htdocs\PROJECTS\baidees\app\Models\FrontendModel.php(34): CodeIgniter\Database\BaseBuilder->get()
#3 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Frontend.php(35): App\Models\FrontendModel->getHomepgbanner()
#4 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Frontend->index()
#5 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#6 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#7 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#8 {main}
CRITICAL - 2021-12-13 23:12:43 --> array_merge(): Expected parameter 1 to be an array, null given
#0 [internal function]: CodeIgniter\Debug\Exceptions->errorHandler(2, 'array_merge(): ...', 'E:\\xampp\\htdocs...', 43, Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Frontend.php(43): array_merge(NULL)
#2 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Frontend->index()
#3 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#4 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#5 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#6 {main}
CRITICAL - 2021-12-13 23:13:03 --> array_merge(): Expected parameter 1 to be an array, null given
#0 [internal function]: CodeIgniter\Debug\Exceptions->errorHandler(2, 'array_merge(): ...', 'E:\\xampp\\htdocs...', 43, Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Frontend.php(43): array_merge(NULL)
#2 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Frontend->index()
#3 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#4 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#5 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#6 {main}
CRITICAL - 2021-12-13 23:13:04 --> array_merge(): Expected parameter 1 to be an array, null given
#0 [internal function]: CodeIgniter\Debug\Exceptions->errorHandler(2, 'array_merge(): ...', 'E:\\xampp\\htdocs...', 43, Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Frontend.php(43): array_merge(NULL)
#2 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Frontend->index()
#3 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#4 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#5 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#6 {main}
CRITICAL - 2021-12-13 23:13:24 --> array_merge(): Expected parameter 1 to be an array, null given
#0 [internal function]: CodeIgniter\Debug\Exceptions->errorHandler(2, 'array_merge(): ...', 'E:\\xampp\\htdocs...', 44, Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Frontend.php(44): array_merge(NULL)
#2 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Frontend->index()
#3 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#4 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#5 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#6 {main}
CRITICAL - 2021-12-13 23:13:26 --> array_merge(): Expected parameter 1 to be an array, null given
#0 [internal function]: CodeIgniter\Debug\Exceptions->errorHandler(2, 'array_merge(): ...', 'E:\\xampp\\htdocs...', 44, Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Frontend.php(44): array_merge(NULL)
#2 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Frontend->index()
#3 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#4 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#5 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#6 {main}
CRITICAL - 2021-12-13 23:13:49 --> Invalid argument supplied for foreach()
#0 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Frontend.php(45): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Invalid argumen...', 'E:\\xampp\\htdocs...', 45, Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Frontend->index()
#2 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#3 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#4 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#5 {main}
CRITICAL - 2021-12-13 23:13:50 --> Invalid argument supplied for foreach()
#0 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Frontend.php(45): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Invalid argumen...', 'E:\\xampp\\htdocs...', 45, Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Frontend->index()
#2 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#3 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#4 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#5 {main}
CRITICAL - 2021-12-13 23:19:34 --> explode() expects parameter 2 to be string, array given
#0 [internal function]: CodeIgniter\Debug\Exceptions->errorHandler(2, 'explode() expec...', 'E:\\xampp\\htdocs...', 50, Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Frontend.php(50): explode(', ', Array)
#2 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Frontend->index()
#3 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#4 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#5 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#6 {main}
CRITICAL - 2021-12-13 23:20:13 --> implode(): Invalid arguments passed
#0 [internal function]: CodeIgniter\Debug\Exceptions->errorHandler(2, 'implode(): Inva...', 'E:\\xampp\\htdocs...', 58, Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Frontend.php(58): implode(', ', NULL)
#2 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Frontend->index()
#3 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#4 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#5 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#6 {main}
CRITICAL - 2021-12-13 23:21:21 --> implode(): Invalid arguments passed
#0 [internal function]: CodeIgniter\Debug\Exceptions->errorHandler(2, 'implode(): Inva...', 'E:\\xampp\\htdocs...', 52, Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Frontend.php(52): implode(', ', 'Skincare')
#2 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Frontend->index()
#3 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#4 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#5 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#6 {main}
CRITICAL - 2021-12-13 23:22:19 --> implode(): Invalid arguments passed
#0 [internal function]: CodeIgniter\Debug\Exceptions->errorHandler(2, 'implode(): Inva...', 'E:\\xampp\\htdocs...', 55, Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Frontend.php(55): implode(', ', 'Bridal')
#2 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Frontend->index()
#3 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#4 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#5 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#6 {main}
CRITICAL - 2021-12-13 23:22:20 --> implode(): Invalid arguments passed
#0 [internal function]: CodeIgniter\Debug\Exceptions->errorHandler(2, 'implode(): Inva...', 'E:\\xampp\\htdocs...', 55, Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Frontend.php(55): implode(', ', 'Bridal')
#2 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Frontend->index()
#3 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#4 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#5 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#6 {main}
CRITICAL - 2021-12-13 23:23:38 --> implode(): Invalid arguments passed
#0 [internal function]: CodeIgniter\Debug\Exceptions->errorHandler(2, 'implode(): Inva...', 'E:\\xampp\\htdocs...', 56, Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Frontend.php(56): implode(', ', 'Bridal')
#2 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Frontend->index()
#3 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#4 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#5 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#6 {main}
CRITICAL - 2021-12-13 23:28:25 --> implode(): Invalid arguments passed
#0 [internal function]: CodeIgniter\Debug\Exceptions->errorHandler(2, 'implode(): Inva...', 'E:\\xampp\\htdocs...', 54, Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Frontend.php(54): implode(', ', 'Skincare')
#2 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Frontend->index()
#3 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#4 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#5 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#6 {main}
CRITICAL - 2021-12-13 23:31:42 --> explode() expects parameter 2 to be string, array given
#0 [internal function]: CodeIgniter\Debug\Exceptions->errorHandler(2, 'explode() expec...', 'E:\\xampp\\htdocs...', 54, Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Frontend.php(54): explode(' ', Array)
#2 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Frontend->index()
#3 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#4 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#5 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#6 {main}
CRITICAL - 2021-12-13 23:31:54 --> implode(): Invalid arguments passed
#0 [internal function]: CodeIgniter\Debug\Exceptions->errorHandler(2, 'implode(): Inva...', 'E:\\xampp\\htdocs...', 55, Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Frontend.php(55): implode(', ', NULL)
#2 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Frontend->index()
#3 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#4 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#5 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#6 {main}
CRITICAL - 2021-12-13 23:31:55 --> implode(): Invalid arguments passed
#0 [internal function]: CodeIgniter\Debug\Exceptions->errorHandler(2, 'implode(): Inva...', 'E:\\xampp\\htdocs...', 55, Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Frontend.php(55): implode(', ', NULL)
#2 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Frontend->index()
#3 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#4 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#5 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#6 {main}
CRITICAL - 2021-12-13 23:31:55 --> implode(): Invalid arguments passed
#0 [internal function]: CodeIgniter\Debug\Exceptions->errorHandler(2, 'implode(): Inva...', 'E:\\xampp\\htdocs...', 55, Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Frontend.php(55): implode(', ', NULL)
#2 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Frontend->index()
#3 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#4 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#5 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#6 {main}
CRITICAL - 2021-12-13 23:31:56 --> implode(): Invalid arguments passed
#0 [internal function]: CodeIgniter\Debug\Exceptions->errorHandler(2, 'implode(): Inva...', 'E:\\xampp\\htdocs...', 55, Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Frontend.php(55): implode(', ', NULL)
#2 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Frontend->index()
#3 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#4 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#5 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#6 {main}
CRITICAL - 2021-12-13 23:31:56 --> implode(): Invalid arguments passed
#0 [internal function]: CodeIgniter\Debug\Exceptions->errorHandler(2, 'implode(): Inva...', 'E:\\xampp\\htdocs...', 55, Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Frontend.php(55): implode(', ', NULL)
#2 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Frontend->index()
#3 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#4 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#5 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#6 {main}
CRITICAL - 2021-12-13 23:32:51 --> explode() expects parameter 2 to be string, array given
#0 [internal function]: CodeIgniter\Debug\Exceptions->errorHandler(2, 'explode() expec...', 'E:\\xampp\\htdocs...', 50, Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Frontend.php(50): explode(' ', Array)
#2 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Frontend->index()
#3 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#4 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#5 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#6 {main}
CRITICAL - 2021-12-13 23:35:15 --> explode() expects parameter 2 to be string, array given
#0 [internal function]: CodeIgniter\Debug\Exceptions->errorHandler(2, 'explode() expec...', 'E:\\xampp\\htdocs...', 53, Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Frontend.php(53): explode(' ', Array)
#2 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Frontend->index()
#3 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#4 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#5 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#6 {main}
CRITICAL - 2021-12-13 23:38:58 --> implode(): Invalid arguments passed
#0 [internal function]: CodeIgniter\Debug\Exceptions->errorHandler(2, 'implode(): Inva...', 'E:\\xampp\\htdocs...', 46, Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Frontend.php(46): implode(',', NULL)
#2 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Frontend->index()
#3 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#4 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#5 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#6 {main}
CRITICAL - 2021-12-13 23:44:23 --> implode(): Invalid arguments passed
#0 [internal function]: CodeIgniter\Debug\Exceptions->errorHandler(2, 'implode(): Inva...', 'E:\\xampp\\htdocs...', 51, Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Frontend.php(51): implode(',', 'Skincare')
#2 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Frontend->index()
#3 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#4 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#5 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#6 {main}
CRITICAL - 2021-12-13 23:44:28 --> implode(): Invalid arguments passed
#0 [internal function]: CodeIgniter\Debug\Exceptions->errorHandler(2, 'implode(): Inva...', 'E:\\xampp\\htdocs...', 51, Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Frontend.php(51): implode(',', 'Skincare')
#2 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Frontend->index()
#3 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#4 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#5 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#6 {main}
