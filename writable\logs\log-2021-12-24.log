CRITICAL - 2021-12-24 00:59:02 --> Call to undefined function App\Controllers\Admin\getProductIdNames()
#0 E:\web6\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Salesentry->savesalesentry()
#1 E:\web6\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Salesentry))
#2 E:\web6\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#3 E:\web6\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#4 {main}
CRITICAL - 2021-12-24 03:45:00 --> strtotime(): Epoch doesn't fit in a PHP integer
#0 [internal function]: CodeIgniter\Debug\Exceptions->errorHandler(2, 'strtotime(): Ep...', 'E:\\web6\\baidees...', 64)
#1 E:\web6\baidees\app\Views\admin\order\vieworder.php(64): strtotime('0000-00-00')
#2 E:\web6\baidees\system\View\View.php(220): include('E:\\web6\\baidees...')
#3 E:\web6\baidees\system\View\View.php(222): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#4 E:\web6\baidees\system\Common.php(1217): CodeIgniter\View\View->render('admin/order/vie...', Array, true)
#5 E:\web6\baidees\app\Controllers\Admin\Order.php(47): view('admin/order/vie...', Array)
#6 E:\web6\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Order->vieworder('7647966b7343c29...')
#7 E:\web6\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Order))
#8 E:\web6\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\baidees\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2021-12-24 04:06:18 --> strtotime(): Epoch doesn't fit in a PHP integer
#0 [internal function]: CodeIgniter\Debug\Exceptions->errorHandler(2, 'strtotime(): Ep...', 'E:\\web6\\baidees...', 64)
#1 E:\web6\baidees\app\Views\admin\order\vieworder.php(64): strtotime('0000-00-00')
#2 E:\web6\baidees\system\View\View.php(220): include('E:\\web6\\baidees...')
#3 E:\web6\baidees\system\View\View.php(222): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#4 E:\web6\baidees\system\Common.php(1217): CodeIgniter\View\View->render('admin/order/vie...', Array, true)
#5 E:\web6\baidees\app\Controllers\Admin\Order.php(47): view('admin/order/vie...', Array)
#6 E:\web6\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Order->vieworder('7647966b7343c29...')
#7 E:\web6\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Order))
#8 E:\web6\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\baidees\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2021-12-24 04:48:44 --> Attempt to read property "product_id" on array
#0 E:\web6\baidees\app\Views\admin\product\addproduct.php(220): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Attempt to read...', 'E:\\web6\\baidees...', 220)
#1 E:\web6\baidees\system\View\View.php(220): include('E:\\web6\\baidees...')
#2 E:\web6\baidees\system\View\View.php(222): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#3 E:\web6\baidees\system\Common.php(1217): CodeIgniter\View\View->render('admin/product/a...', Array, true)
#4 E:\web6\baidees\app\Controllers\Admin\Product.php(27): view('admin/product/a...', Array)
#5 E:\web6\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Product->addproduct()
#6 E:\web6\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Product))
#7 E:\web6\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#8 E:\web6\baidees\index.php(48): CodeIgniter\CodeIgniter->run()
#9 {main}
CRITICAL - 2021-12-24 05:17:58 --> Undefined variable $metatitle
#0 E:\web6\baidees\app\Views\frontend_view\layouts\layout.php(12): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Undefined varia...', 'E:\\web6\\baidees...', 12)
#1 E:\web6\baidees\system\View\View.php(220): include('E:\\web6\\baidees...')
#2 E:\web6\baidees\system\View\View.php(222): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#3 E:\web6\baidees\system\View\View.php(236): CodeIgniter\View\View->render('frontend_view/l...', Array, true)
#4 E:\web6\baidees\system\Common.php(1217): CodeIgniter\View\View->render('frontend_view/4...', Array, true)
#5 E:\web6\baidees\app\Controllers\Frontend.php(1033): view('frontend_view/4...', Array)
#6 E:\web6\baidees\system\CodeIgniter.php(936): App\Controllers\Frontend->_404()
#7 E:\web6\baidees\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\baidees\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#9 E:\web6\baidees\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2021-12-24 05:18:51 --> Undefined variable $metatitle
#0 E:\web6\baidees\app\Views\frontend_view\layouts\layout.php(12): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Undefined varia...', 'E:\\web6\\baidees...', 12)
#1 E:\web6\baidees\system\View\View.php(220): include('E:\\web6\\baidees...')
#2 E:\web6\baidees\system\View\View.php(222): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#3 E:\web6\baidees\system\View\View.php(236): CodeIgniter\View\View->render('frontend_view/l...', Array, true)
#4 E:\web6\baidees\system\Common.php(1217): CodeIgniter\View\View->render('frontend_view/4...', Array, true)
#5 E:\web6\baidees\app\Controllers\Frontend.php(1033): view('frontend_view/4...', Array)
#6 E:\web6\baidees\system\CodeIgniter.php(936): App\Controllers\Frontend->_404()
#7 E:\web6\baidees\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\baidees\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#9 E:\web6\baidees\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2021-12-24 05:19:31 --> Undefined variable $metatitle
#0 E:\web6\baidees\app\Views\frontend_view\layouts\layout.php(12): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Undefined varia...', 'E:\\web6\\baidees...', 12)
#1 E:\web6\baidees\system\View\View.php(220): include('E:\\web6\\baidees...')
#2 E:\web6\baidees\system\View\View.php(222): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#3 E:\web6\baidees\system\View\View.php(236): CodeIgniter\View\View->render('frontend_view/l...', Array, true)
#4 E:\web6\baidees\system\Common.php(1217): CodeIgniter\View\View->render('frontend_view/4...', Array, true)
#5 E:\web6\baidees\app\Controllers\Frontend.php(1033): view('frontend_view/4...', Array)
#6 E:\web6\baidees\system\CodeIgniter.php(936): App\Controllers\Frontend->_404()
#7 E:\web6\baidees\system\CodeIgniter.php(970): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\baidees\system\CodeIgniter.php(349): CodeIgniter\CodeIgniter->display404errors(Object(CodeIgniter\Exceptions\PageNotFoundException))
#9 E:\web6\baidees\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2021-12-24 05:49:14 --> Undefined variable $orderlist
#0 E:\web6\baidees\app\Controllers\Admin\Order.php(38): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Undefined varia...', 'E:\\web6\\baidees...', 38)
#1 E:\web6\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Order->ajaxlistorders()
#2 E:\web6\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Order))
#3 E:\web6\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#4 E:\web6\baidees\index.php(48): CodeIgniter\CodeIgniter->run()
#5 {main}
CRITICAL - 2021-12-24 05:55:34 --> Undefined variable $c_email
#0 E:\web6\baidees\app\Controllers\Frontend.php(522): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Undefined varia...', 'E:\\web6\\baidees...', 522)
#1 E:\web6\baidees\system\CodeIgniter.php(936): App\Controllers\Frontend->orderdetails()
#2 E:\web6\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#3 E:\web6\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#4 E:\web6\baidees\index.php(48): CodeIgniter\CodeIgniter->run()
#5 {main}
CRITICAL - 2021-12-24 05:55:38 --> Undefined variable $c_email
#0 E:\web6\baidees\app\Controllers\Frontend.php(522): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Undefined varia...', 'E:\\web6\\baidees...', 522)
#1 E:\web6\baidees\system\CodeIgniter.php(936): App\Controllers\Frontend->orderdetails()
#2 E:\web6\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#3 E:\web6\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#4 E:\web6\baidees\index.php(48): CodeIgniter\CodeIgniter->run()
#5 {main}
CRITICAL - 2021-12-24 05:55:52 --> Undefined variable $c_email
#0 E:\web6\baidees\app\Controllers\Frontend.php(522): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Undefined varia...', 'E:\\web6\\baidees...', 522)
#1 E:\web6\baidees\system\CodeIgniter.php(936): App\Controllers\Frontend->orderdetails()
#2 E:\web6\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#3 E:\web6\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#4 E:\web6\baidees\index.php(48): CodeIgniter\CodeIgniter->run()
#5 {main}
CRITICAL - 2021-12-24 05:58:12 --> Undefined variable $metadesc
#0 E:\web6\baidees\app\Views\frontend_view\layouts\layout.php(13): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Undefined varia...', 'E:\\web6\\baidees...', 13)
#1 E:\web6\baidees\system\View\View.php(220): include('E:\\web6\\baidees...')
#2 E:\web6\baidees\system\View\View.php(222): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#3 E:\web6\baidees\system\View\View.php(236): CodeIgniter\View\View->render('frontend_view/l...', Array, true)
#4 E:\web6\baidees\system\Common.php(1217): CodeIgniter\View\View->render('frontend_view/v...', Array, true)
#5 E:\web6\baidees\app\Controllers\Frontend.php(1024): view('frontend_view/v...', Array)
#6 E:\web6\baidees\system\CodeIgniter.php(936): App\Controllers\Frontend->vieworder('', '7f1de29e6da19d2...')
#7 E:\web6\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\baidees\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2021-12-24 05:59:18 --> Undefined variable $metakeywords
#0 E:\web6\baidees\app\Views\frontend_view\layouts\layout.php(14): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Undefined varia...', 'E:\\web6\\baidees...', 14)
#1 E:\web6\baidees\system\View\View.php(220): include('E:\\web6\\baidees...')
#2 E:\web6\baidees\system\View\View.php(222): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#3 E:\web6\baidees\system\View\View.php(236): CodeIgniter\View\View->render('frontend_view/l...', Array, true)
#4 E:\web6\baidees\system\Common.php(1217): CodeIgniter\View\View->render('frontend_view/v...', Array, true)
#5 E:\web6\baidees\app\Controllers\Frontend.php(1025): view('frontend_view/v...', Array)
#6 E:\web6\baidees\system\CodeIgniter.php(936): App\Controllers\Frontend->vieworder('', '7f1de29e6da19d2...')
#7 E:\web6\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\baidees\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2021-12-24 05:59:41 --> Undefined variable $metakeywords
#0 E:\web6\baidees\app\Views\frontend_view\layouts\layout.php(14): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Undefined varia...', 'E:\\web6\\baidees...', 14)
#1 E:\web6\baidees\system\View\View.php(220): include('E:\\web6\\baidees...')
#2 E:\web6\baidees\system\View\View.php(222): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#3 E:\web6\baidees\system\View\View.php(236): CodeIgniter\View\View->render('frontend_view/l...', Array, true)
#4 E:\web6\baidees\system\Common.php(1217): CodeIgniter\View\View->render('frontend_view/v...', Array, true)
#5 E:\web6\baidees\app\Controllers\Frontend.php(1026): view('frontend_view/v...', Array)
#6 E:\web6\baidees\system\CodeIgniter.php(936): App\Controllers\Frontend->vieworder('', '7f1de29e6da19d2...')
#7 E:\web6\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\baidees\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2021-12-24 05:59:44 --> Undefined variable $metakeywords
#0 E:\web6\baidees\app\Views\frontend_view\layouts\layout.php(14): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Undefined varia...', 'E:\\web6\\baidees...', 14)
#1 E:\web6\baidees\system\View\View.php(220): include('E:\\web6\\baidees...')
#2 E:\web6\baidees\system\View\View.php(222): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#3 E:\web6\baidees\system\View\View.php(236): CodeIgniter\View\View->render('frontend_view/l...', Array, true)
#4 E:\web6\baidees\system\Common.php(1217): CodeIgniter\View\View->render('frontend_view/v...', Array, true)
#5 E:\web6\baidees\app\Controllers\Frontend.php(1026): view('frontend_view/v...', Array)
#6 E:\web6\baidees\system\CodeIgniter.php(936): App\Controllers\Frontend->vieworder('', '7f1de29e6da19d2...')
#7 E:\web6\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\baidees\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2021-12-24 06:00:06 --> Undefined variable $metakeywords
#0 E:\web6\baidees\app\Views\frontend_view\layouts\layout.php(14): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Undefined varia...', 'E:\\web6\\baidees...', 14)
#1 E:\web6\baidees\system\View\View.php(220): include('E:\\web6\\baidees...')
#2 E:\web6\baidees\system\View\View.php(222): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#3 E:\web6\baidees\system\View\View.php(236): CodeIgniter\View\View->render('frontend_view/l...', Array, true)
#4 E:\web6\baidees\system\Common.php(1217): CodeIgniter\View\View->render('frontend_view/v...', Array, true)
#5 E:\web6\baidees\app\Controllers\Frontend.php(1026): view('frontend_view/v...', Array)
#6 E:\web6\baidees\system\CodeIgniter.php(936): App\Controllers\Frontend->vieworder('', '7f1de29e6da19d2...')
#7 E:\web6\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\baidees\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2021-12-24 06:00:18 --> Undefined variable $ogimagepath
#0 E:\web6\baidees\app\Views\frontend_view\layouts\layout.php(16): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Undefined varia...', 'E:\\web6\\baidees...', 16)
#1 E:\web6\baidees\system\View\View.php(220): include('E:\\web6\\baidees...')
#2 E:\web6\baidees\system\View\View.php(222): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#3 E:\web6\baidees\system\View\View.php(236): CodeIgniter\View\View->render('frontend_view/l...', Array, true)
#4 E:\web6\baidees\system\Common.php(1217): CodeIgniter\View\View->render('frontend_view/v...', Array, true)
#5 E:\web6\baidees\app\Controllers\Frontend.php(1026): view('frontend_view/v...', Array)
#6 E:\web6\baidees\system\CodeIgniter.php(936): App\Controllers\Frontend->vieworder('', '7f1de29e6da19d2...')
#7 E:\web6\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\web6\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\web6\baidees\index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2021-12-24 06:33:15 --> Undefined variable $orderlist
#0 E:\web6\baidees\app\Controllers\Admin\Order.php(39): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Undefined varia...', 'E:\\web6\\baidees...', 39)
#1 E:\web6\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Order->ajaxlistorders()
#2 E:\web6\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Order))
#3 E:\web6\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#4 E:\web6\baidees\index.php(48): CodeIgniter\CodeIgniter->run()
#5 {main}
CRITICAL - 2021-12-24 06:33:26 --> Undefined variable $orderlist
#0 E:\web6\baidees\app\Controllers\Admin\Order.php(39): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Undefined varia...', 'E:\\web6\\baidees...', 39)
#1 E:\web6\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Order->ajaxlistorders()
#2 E:\web6\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Order))
#3 E:\web6\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#4 E:\web6\baidees\index.php(48): CodeIgniter\CodeIgniter->run()
#5 {main}
CRITICAL - 2021-12-24 07:11:02 --> Undefined variable $order
#0 E:\web6\baidees\app\Views\admin\order\orderlist.php(111): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Undefined varia...', 'E:\\web6\\baidees...', 111)
#1 E:\web6\baidees\system\View\View.php(220): include('E:\\web6\\baidees...')
#2 E:\web6\baidees\system\View\View.php(222): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#3 E:\web6\baidees\system\Common.php(1217): CodeIgniter\View\View->render('admin/order/ord...', Array, true)
#4 E:\web6\baidees\app\Controllers\Admin\Order.php(23): view('admin/order/ord...', Array)
#5 E:\web6\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Order->indexall()
#6 E:\web6\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Order))
#7 E:\web6\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#8 E:\web6\baidees\index.php(48): CodeIgniter\CodeIgniter->run()
#9 {main}
CRITICAL - 2021-12-24 07:11:16 --> Undefined variable $order
#0 E:\web6\baidees\app\Views\admin\order\orderlist.php(111): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Undefined varia...', 'E:\\web6\\baidees...', 111)
#1 E:\web6\baidees\system\View\View.php(220): include('E:\\web6\\baidees...')
#2 E:\web6\baidees\system\View\View.php(222): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#3 E:\web6\baidees\system\Common.php(1217): CodeIgniter\View\View->render('admin/order/ord...', Array, true)
#4 E:\web6\baidees\app\Controllers\Admin\Order.php(23): view('admin/order/ord...', Array)
#5 E:\web6\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Order->indexall()
#6 E:\web6\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Order))
#7 E:\web6\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#8 E:\web6\baidees\index.php(48): CodeIgniter\CodeIgniter->run()
#9 {main}
CRITICAL - 2021-12-24 07:11:47 --> Undefined variable $order
#0 E:\web6\baidees\app\Views\admin\order\orderlist.php(111): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Undefined varia...', 'E:\\web6\\baidees...', 111)
#1 E:\web6\baidees\system\View\View.php(220): include('E:\\web6\\baidees...')
#2 E:\web6\baidees\system\View\View.php(222): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#3 E:\web6\baidees\system\Common.php(1217): CodeIgniter\View\View->render('admin/order/ord...', Array, true)
#4 E:\web6\baidees\app\Controllers\Admin\Order.php(23): view('admin/order/ord...', Array)
#5 E:\web6\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Order->indexall()
#6 E:\web6\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Order))
#7 E:\web6\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#8 E:\web6\baidees\index.php(48): CodeIgniter\CodeIgniter->run()
#9 {main}
