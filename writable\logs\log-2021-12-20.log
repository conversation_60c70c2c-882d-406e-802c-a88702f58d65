CRITICAL - 2021-12-20 03:39:27 --> Call to a member function select() on null
#0 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Admin\Salesentry.php(14): App\Models\SalesentryModel->getOrderList(2)
#1 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Salesentry->index()
#2 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Salesentry))
#3 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#4 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#5 {main}
CRITICAL - 2021-12-20 03:45:21 --> Invalid file: admin/order/salesentrylist.php
#0 E:\xampp\htdocs\PROJECTS\baidees\system\View\View.php(203): CodeIgniter\Exceptions\FrameworkException::forInvalidFile('admin/order/sal...')
#1 E:\xampp\htdocs\PROJECTS\baidees\system\Common.php(1217): CodeIgniter\View\View->render('admin/order/sal...', Array, true)
#2 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Admin\Salesentry.php(15): view('admin/order/sal...', Array)
#3 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Salesentry->index()
#4 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Salesentry))
#5 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#6 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#7 {main}
ERROR - 2021-12-20 03:58:52 --> mysqli_sql_exception: Unknown column 'todr.staff_id' in 'on clause' in E:\xampp\htdocs\PROJECTS\baidees\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\xampp\htdocs\PROJECTS\baidees\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `todr`.*...')
#1 E:\xampp\htdocs\PROJECTS\baidees\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `todr`.*...')
#2 E:\xampp\htdocs\PROJECTS\baidees\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `todr`.*...')
#3 E:\xampp\htdocs\PROJECTS\baidees\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `todr`.*...', Array, false)
#4 E:\xampp\htdocs\PROJECTS\baidees\app\Models\SalesentryModel.php(44): CodeIgniter\Database\BaseBuilder->get()
#5 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Admin\Salesentry.php(14): App\Models\SalesentryModel->getOrderList(2)
#6 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Salesentry->index()
#7 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Salesentry))
#8 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2021-12-20 03:58:55 --> mysqli_sql_exception: Unknown column 'todr.staff_id' in 'on clause' in E:\xampp\htdocs\PROJECTS\baidees\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\xampp\htdocs\PROJECTS\baidees\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `todr`.*...')
#1 E:\xampp\htdocs\PROJECTS\baidees\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `todr`.*...')
#2 E:\xampp\htdocs\PROJECTS\baidees\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `todr`.*...')
#3 E:\xampp\htdocs\PROJECTS\baidees\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `todr`.*...', Array, false)
#4 E:\xampp\htdocs\PROJECTS\baidees\app\Models\SalesentryModel.php(44): CodeIgniter\Database\BaseBuilder->get()
#5 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Admin\Salesentry.php(14): App\Models\SalesentryModel->getOrderList(2)
#6 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Salesentry->index()
#7 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Salesentry))
#8 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2021-12-20 03:58:55 --> mysqli_sql_exception: Unknown column 'todr.staff_id' in 'on clause' in E:\xampp\htdocs\PROJECTS\baidees\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\xampp\htdocs\PROJECTS\baidees\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `todr`.*...')
#1 E:\xampp\htdocs\PROJECTS\baidees\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `todr`.*...')
#2 E:\xampp\htdocs\PROJECTS\baidees\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `todr`.*...')
#3 E:\xampp\htdocs\PROJECTS\baidees\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `todr`.*...', Array, false)
#4 E:\xampp\htdocs\PROJECTS\baidees\app\Models\SalesentryModel.php(44): CodeIgniter\Database\BaseBuilder->get()
#5 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Admin\Salesentry.php(14): App\Models\SalesentryModel->getOrderList(2)
#6 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Salesentry->index()
#7 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Salesentry))
#8 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2021-12-20 03:59:04 --> mysqli_sql_exception: Unknown column 'todr.staff_id' in 'on clause' in E:\xampp\htdocs\PROJECTS\baidees\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\xampp\htdocs\PROJECTS\baidees\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `todr`.*...')
#1 E:\xampp\htdocs\PROJECTS\baidees\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `todr`.*...')
#2 E:\xampp\htdocs\PROJECTS\baidees\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `todr`.*...')
#3 E:\xampp\htdocs\PROJECTS\baidees\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `todr`.*...', Array, false)
#4 E:\xampp\htdocs\PROJECTS\baidees\app\Models\SalesentryModel.php(44): CodeIgniter\Database\BaseBuilder->get()
#5 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Admin\Salesentry.php(14): App\Models\SalesentryModel->getOrderList(2)
#6 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Salesentry->index()
#7 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Salesentry))
#8 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2021-12-20 04:00:36 --> mysqli_sql_exception: Unknown column 'todr.staff_id' in 'on clause' in E:\xampp\htdocs\PROJECTS\baidees\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\xampp\htdocs\PROJECTS\baidees\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `todr`.*...')
#1 E:\xampp\htdocs\PROJECTS\baidees\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `todr`.*...')
#2 E:\xampp\htdocs\PROJECTS\baidees\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `todr`.*...')
#3 E:\xampp\htdocs\PROJECTS\baidees\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `todr`.*...', Array, false)
#4 E:\xampp\htdocs\PROJECTS\baidees\app\Models\SalesentryModel.php(44): CodeIgniter\Database\BaseBuilder->get()
#5 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Admin\Salesentry.php(14): App\Models\SalesentryModel->getOrderList(2)
#6 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Salesentry->index()
#7 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Salesentry))
#8 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2021-12-20 04:09:33 --> Call to undefined method App\Models\SalesentryModel::getOrderItems
#0 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Admin\Salesentry.php(31): CodeIgniter\Model->__call('getOrderItems', Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Salesentry->vieworder('c9e1074f5b3f9fc...')
#2 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Salesentry))
#3 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#4 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#5 {main}
CRITICAL - 2021-12-20 04:09:34 --> Call to undefined method App\Models\SalesentryModel::getOrderItems
#0 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Admin\Salesentry.php(31): CodeIgniter\Model->__call('getOrderItems', Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Salesentry->vieworder('c9e1074f5b3f9fc...')
#2 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Salesentry))
#3 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#4 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#5 {main}
CRITICAL - 2021-12-20 04:09:35 --> Call to undefined method App\Models\SalesentryModel::getOrderItems
#0 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Admin\Salesentry.php(31): CodeIgniter\Model->__call('getOrderItems', Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Salesentry->vieworder('c9e1074f5b3f9fc...')
#2 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Salesentry))
#3 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#4 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#5 {main}
CRITICAL - 2021-12-20 04:09:35 --> Call to undefined method App\Models\SalesentryModel::getOrderItems
#0 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Admin\Salesentry.php(31): CodeIgniter\Model->__call('getOrderItems', Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Salesentry->vieworder('c9e1074f5b3f9fc...')
#2 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Salesentry))
#3 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#4 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#5 {main}
CRITICAL - 2021-12-20 04:09:35 --> Call to undefined method App\Models\SalesentryModel::getOrderItems
#0 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Admin\Salesentry.php(31): CodeIgniter\Model->__call('getOrderItems', Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Salesentry->vieworder('c9e1074f5b3f9fc...')
#2 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Salesentry))
#3 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#4 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#5 {main}
CRITICAL - 2021-12-20 04:09:35 --> Call to undefined method App\Models\SalesentryModel::getOrderItems
#0 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Admin\Salesentry.php(31): CodeIgniter\Model->__call('getOrderItems', Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Salesentry->vieworder('c9e1074f5b3f9fc...')
#2 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Salesentry))
#3 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#4 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#5 {main}
CRITICAL - 2021-12-20 05:29:21 --> array_sum() expects parameter 1 to be array, string given
#0 [internal function]: CodeIgniter\Debug\Exceptions->errorHandler(2, 'array_sum() exp...', 'E:\\xampp\\htdocs...', 154, Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Admin\Salesentry.php(154): array_sum('500')
#2 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Salesentry->savesalesentry()
#3 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Salesentry))
#4 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#5 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#6 {main}
CRITICAL - 2021-12-20 05:30:34 --> array_sum() expects parameter 1 to be array, string given
#0 [internal function]: CodeIgniter\Debug\Exceptions->errorHandler(2, 'array_sum() exp...', 'E:\\xampp\\htdocs...', 153, Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Admin\Salesentry.php(153): array_sum('500')
#2 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Salesentry->savesalesentry()
#3 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Salesentry))
#4 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#5 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#6 {main}
CRITICAL - 2021-12-20 05:32:14 --> array_sum() expects parameter 1 to be array, string given
#0 [internal function]: CodeIgniter\Debug\Exceptions->errorHandler(2, 'array_sum() exp...', 'E:\\xampp\\htdocs...', 152, Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Admin\Salesentry.php(152): array_sum('500')
#2 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Salesentry->savesalesentry()
#3 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Salesentry))
#4 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#5 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#6 {main}
CRITICAL - 2021-12-20 05:32:38 --> array_sum() expects parameter 1 to be array, string given
#0 [internal function]: CodeIgniter\Debug\Exceptions->errorHandler(2, 'array_sum() exp...', 'E:\\xampp\\htdocs...', 152, Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Admin\Salesentry.php(152): array_sum('500')
#2 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Salesentry->savesalesentry()
#3 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Salesentry))
#4 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#5 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#6 {main}
ERROR - 2021-12-20 05:52:52 --> mysqli_sql_exception: Unknown column 'product_id' in 'field list' in E:\xampp\htdocs\PROJECTS\baidees\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\xampp\htdocs\PROJECTS\baidees\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `product...')
#1 E:\xampp\htdocs\PROJECTS\baidees\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `product...')
#2 E:\xampp\htdocs\PROJECTS\baidees\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `product...')
#3 E:\xampp\htdocs\PROJECTS\baidees\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `product...', Array, false)
#4 E:\xampp\htdocs\PROJECTS\baidees\app\Models\SalesentryModel.php(77): CodeIgniter\Database\BaseBuilder->get()
#5 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Admin\Salesentry.php(193): App\Models\SalesentryModel->getProdNameById('17')
#6 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Salesentry->savesalesentry()
#7 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Salesentry))
#8 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2021-12-20 05:52:57 --> mysqli_sql_exception: Unknown column 'product_id' in 'field list' in E:\xampp\htdocs\PROJECTS\baidees\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\xampp\htdocs\PROJECTS\baidees\system\Database\MySQLi\Connection.php(314): mysqli->query('SELECT `product...')
#1 E:\xampp\htdocs\PROJECTS\baidees\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `product...')
#2 E:\xampp\htdocs\PROJECTS\baidees\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `product...')
#3 E:\xampp\htdocs\PROJECTS\baidees\system\Database\BaseBuilder.php(1866): CodeIgniter\Database\BaseConnection->query('SELECT `product...', Array, false)
#4 E:\xampp\htdocs\PROJECTS\baidees\app\Models\SalesentryModel.php(77): CodeIgniter\Database\BaseBuilder->get()
#5 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Admin\Salesentry.php(193): App\Models\SalesentryModel->getProdNameById('17')
#6 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Salesentry->savesalesentry()
#7 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Salesentry))
#8 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2021-12-20 06:09:03 --> syntax error, unexpected ')', expecting '['
#0 E:\xampp\htdocs\PROJECTS\baidees\system\Autoloader\Autoloader.php(265): CodeIgniter\Autoloader\Autoloader->includeFile('E:\\xampp\\htdocs...')
#1 E:\xampp\htdocs\PROJECTS\baidees\system\Autoloader\Autoloader.php(228): CodeIgniter\Autoloader\Autoloader->loadInNamespace('App\\Controllers...')
#2 [internal function]: CodeIgniter\Autoloader\Autoloader->loadClass('App\\Controllers...')
#3 [internal function]: spl_autoload_call('App\\Controllers...')
#4 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(893): class_exists('\\App\\Controller...', true)
#5 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(417): CodeIgniter\CodeIgniter->startController()
#6 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#7 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#8 {main}
CRITICAL - 2021-12-20 06:10:08 --> Invalid argument supplied for foreach()
#0 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Admin\Salesentry.php(156): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Invalid argumen...', 'E:\\xampp\\htdocs...', 156, Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Salesentry->savesalesentry()
#2 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Salesentry))
#3 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#4 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#5 {main}
CRITICAL - 2021-12-20 06:10:12 --> Invalid argument supplied for foreach()
#0 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Admin\Salesentry.php(156): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Invalid argumen...', 'E:\\xampp\\htdocs...', 156, Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Salesentry->savesalesentry()
#2 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Salesentry))
#3 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#4 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#5 {main}
CRITICAL - 2021-12-20 06:10:19 --> Invalid argument supplied for foreach()
#0 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Admin\Salesentry.php(156): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Invalid argumen...', 'E:\\xampp\\htdocs...', 156, Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Salesentry->savesalesentry()
#2 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Salesentry))
#3 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#4 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#5 {main}
CRITICAL - 2021-12-20 06:10:34 --> Invalid argument supplied for foreach()
#0 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Admin\Salesentry.php(156): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Invalid argumen...', 'E:\\xampp\\htdocs...', 156, Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Salesentry->savesalesentry()
#2 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Salesentry))
#3 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#4 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#5 {main}
CRITICAL - 2021-12-20 06:10:34 --> Invalid argument supplied for foreach()
#0 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Admin\Salesentry.php(156): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Invalid argumen...', 'E:\\xampp\\htdocs...', 156, Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Salesentry->savesalesentry()
#2 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Salesentry))
#3 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#4 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#5 {main}
CRITICAL - 2021-12-20 06:11:52 --> Illegal string offset '.$j.'
#0 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Admin\Salesentry.php(156): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Illegal string ...', 'E:\\xampp\\htdocs...', 156, Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Salesentry->savesalesentry()
#2 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Salesentry))
#3 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#4 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#5 {main}
CRITICAL - 2021-12-20 06:12:02 --> Illegal string offset '.$j.'
#0 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Admin\Salesentry.php(156): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Illegal string ...', 'E:\\xampp\\htdocs...', 156, Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Salesentry->savesalesentry()
#2 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Salesentry))
#3 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#4 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#5 {main}
CRITICAL - 2021-12-20 06:12:03 --> Illegal string offset '.$j.'
#0 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Admin\Salesentry.php(156): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Illegal string ...', 'E:\\xampp\\htdocs...', 156, Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Salesentry->savesalesentry()
#2 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Salesentry))
#3 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#4 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#5 {main}
CRITICAL - 2021-12-20 06:12:04 --> Illegal string offset '.$j.'
#0 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Admin\Salesentry.php(156): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Illegal string ...', 'E:\\xampp\\htdocs...', 156, Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Salesentry->savesalesentry()
#2 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Salesentry))
#3 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#4 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#5 {main}
CRITICAL - 2021-12-20 06:12:04 --> Illegal string offset '.$j.'
#0 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Admin\Salesentry.php(156): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Illegal string ...', 'E:\\xampp\\htdocs...', 156, Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Salesentry->savesalesentry()
#2 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Salesentry))
#3 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#4 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#5 {main}
CRITICAL - 2021-12-20 06:12:04 --> Illegal string offset '.$j.'
#0 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Admin\Salesentry.php(156): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Illegal string ...', 'E:\\xampp\\htdocs...', 156, Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Salesentry->savesalesentry()
#2 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Salesentry))
#3 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#4 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#5 {main}
CRITICAL - 2021-12-20 06:12:04 --> Illegal string offset '.$j.'
#0 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Admin\Salesentry.php(156): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Illegal string ...', 'E:\\xampp\\htdocs...', 156, Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Salesentry->savesalesentry()
#2 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Salesentry))
#3 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#4 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#5 {main}
CRITICAL - 2021-12-20 06:12:05 --> Illegal string offset '.$j.'
#0 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Admin\Salesentry.php(156): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Illegal string ...', 'E:\\xampp\\htdocs...', 156, Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Salesentry->savesalesentry()
#2 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Salesentry))
#3 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#4 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#5 {main}
CRITICAL - 2021-12-20 06:35:04 --> count(): Parameter must be an array or an object that implements Countable
#0 [internal function]: CodeIgniter\Debug\Exceptions->errorHandler(2, 'count(): Parame...', 'E:\\xampp\\htdocs...', 163, Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Admin\Salesentry.php(163): count(2)
#2 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Salesentry->savesalesentry()
#3 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Salesentry))
#4 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#5 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#6 {main}
CRITICAL - 2021-12-20 06:35:28 --> count(): Parameter must be an array or an object that implements Countable
#0 [internal function]: CodeIgniter\Debug\Exceptions->errorHandler(2, 'count(): Parame...', 'E:\\xampp\\htdocs...', 163, Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Admin\Salesentry.php(163): count(2)
#2 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Salesentry->savesalesentry()
#3 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Salesentry))
#4 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#5 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#6 {main}
CRITICAL - 2021-12-20 06:37:49 --> count(): Parameter must be an array or an object that implements Countable
#0 [internal function]: CodeIgniter\Debug\Exceptions->errorHandler(2, 'count(): Parame...', 'E:\\xampp\\htdocs...', 163, Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Admin\Salesentry.php(163): count(2)
#2 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Salesentry->savesalesentry()
#3 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Salesentry))
#4 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#5 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#6 {main}
CRITICAL - 2021-12-20 06:38:44 --> count(): Parameter must be an array or an object that implements Countable
#0 [internal function]: CodeIgniter\Debug\Exceptions->errorHandler(2, 'count(): Parame...', 'E:\\xampp\\htdocs...', 163, Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Admin\Salesentry.php(163): count(2)
#2 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Salesentry->savesalesentry()
#3 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Salesentry))
#4 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#5 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#6 {main}
CRITICAL - 2021-12-20 06:38:59 --> count(): Parameter must be an array or an object that implements Countable
#0 [internal function]: CodeIgniter\Debug\Exceptions->errorHandler(2, 'count(): Parame...', 'E:\\xampp\\htdocs...', 163, Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Admin\Salesentry.php(163): count(2)
#2 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Salesentry->savesalesentry()
#3 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Salesentry))
#4 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#5 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#6 {main}
CRITICAL - 2021-12-20 06:39:36 --> count(): Parameter must be an array or an object that implements Countable
#0 [internal function]: CodeIgniter\Debug\Exceptions->errorHandler(2, 'count(): Parame...', 'E:\\xampp\\htdocs...', 163, Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Admin\Salesentry.php(163): count(2)
#2 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Salesentry->savesalesentry()
#3 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Salesentry))
#4 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#5 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#6 {main}
CRITICAL - 2021-12-20 06:40:07 --> count(): Parameter must be an array or an object that implements Countable
#0 [internal function]: CodeIgniter\Debug\Exceptions->errorHandler(2, 'count(): Parame...', 'E:\\xampp\\htdocs...', 163, Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Admin\Salesentry.php(163): count(2)
#2 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Salesentry->savesalesentry()
#3 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Salesentry))
#4 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#5 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#6 {main}
CRITICAL - 2021-12-20 06:40:18 --> count(): Parameter must be an array or an object that implements Countable
#0 [internal function]: CodeIgniter\Debug\Exceptions->errorHandler(2, 'count(): Parame...', 'E:\\xampp\\htdocs...', 163, Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Admin\Salesentry.php(163): count(2)
#2 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Salesentry->savesalesentry()
#3 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Salesentry))
#4 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#5 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#6 {main}
CRITICAL - 2021-12-20 06:40:32 --> count(): Parameter must be an array or an object that implements Countable
#0 [internal function]: CodeIgniter\Debug\Exceptions->errorHandler(2, 'count(): Parame...', 'E:\\xampp\\htdocs...', 163, Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Admin\Salesentry.php(163): count(2)
#2 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Salesentry->savesalesentry()
#3 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Salesentry))
#4 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#5 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#6 {main}
CRITICAL - 2021-12-20 06:43:57 --> count(): Parameter must be an array or an object that implements Countable
#0 [internal function]: CodeIgniter\Debug\Exceptions->errorHandler(2, 'count(): Parame...', 'E:\\xampp\\htdocs...', 163, Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Admin\Salesentry.php(163): count(2)
#2 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Salesentry->savesalesentry()
#3 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Salesentry))
#4 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#5 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#6 {main}
CRITICAL - 2021-12-20 06:57:48 --> Class 'App\Controllers\Admin\SalesentryModel' not found
#0 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Report->index()
#1 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Report))
#2 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#3 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#4 {main}
CRITICAL - 2021-12-20 06:58:18 --> Class 'App\Controllers\Admin\SalesentryModel' not found
#0 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Report->index()
#1 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Report))
#2 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#3 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#4 {main}
CRITICAL - 2021-12-20 07:03:23 --> Invalid argument supplied for foreach()
#0 E:\xampp\htdocs\PROJECTS\baidees\app\Views\admin\report\reportlist.php(41): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Invalid argumen...', 'E:\\xampp\\htdocs...', 41, Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\system\View\View.php(220): include('E:\\xampp\\htdocs...')
#2 E:\xampp\htdocs\PROJECTS\baidees\system\View\View.php(222): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#3 E:\xampp\htdocs\PROJECTS\baidees\system\Common.php(1217): CodeIgniter\View\View->render('admin/report/re...', Array, true)
#4 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Admin\Report.php(15): view('admin/report/re...', Array)
#5 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Report->index()
#6 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Report))
#7 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#8 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#9 {main}
CRITICAL - 2021-12-20 07:03:40 --> Invalid argument supplied for foreach()
#0 E:\xampp\htdocs\PROJECTS\baidees\app\Views\admin\report\reportlist.php(41): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Invalid argumen...', 'E:\\xampp\\htdocs...', 41, Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\system\View\View.php(220): include('E:\\xampp\\htdocs...')
#2 E:\xampp\htdocs\PROJECTS\baidees\system\View\View.php(222): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#3 E:\xampp\htdocs\PROJECTS\baidees\system\Common.php(1217): CodeIgniter\View\View->render('admin/report/re...', Array, true)
#4 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Admin\Report.php(15): view('admin/report/re...', Array)
#5 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Report->index()
#6 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Report))
#7 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#8 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#9 {main}
CRITICAL - 2021-12-20 07:03:41 --> Invalid argument supplied for foreach()
#0 E:\xampp\htdocs\PROJECTS\baidees\app\Views\admin\report\reportlist.php(41): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Invalid argumen...', 'E:\\xampp\\htdocs...', 41, Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\system\View\View.php(220): include('E:\\xampp\\htdocs...')
#2 E:\xampp\htdocs\PROJECTS\baidees\system\View\View.php(222): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#3 E:\xampp\htdocs\PROJECTS\baidees\system\Common.php(1217): CodeIgniter\View\View->render('admin/report/re...', Array, true)
#4 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Admin\Report.php(15): view('admin/report/re...', Array)
#5 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Report->index()
#6 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Report))
#7 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#8 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#9 {main}
CRITICAL - 2021-12-20 07:03:42 --> Invalid argument supplied for foreach()
#0 E:\xampp\htdocs\PROJECTS\baidees\app\Views\admin\report\reportlist.php(41): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Invalid argumen...', 'E:\\xampp\\htdocs...', 41, Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\system\View\View.php(220): include('E:\\xampp\\htdocs...')
#2 E:\xampp\htdocs\PROJECTS\baidees\system\View\View.php(222): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#3 E:\xampp\htdocs\PROJECTS\baidees\system\Common.php(1217): CodeIgniter\View\View->render('admin/report/re...', Array, true)
#4 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Admin\Report.php(15): view('admin/report/re...', Array)
#5 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Report->index()
#6 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Report))
#7 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#8 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#9 {main}
CRITICAL - 2021-12-20 07:03:42 --> Invalid argument supplied for foreach()
#0 E:\xampp\htdocs\PROJECTS\baidees\app\Views\admin\report\reportlist.php(41): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Invalid argumen...', 'E:\\xampp\\htdocs...', 41, Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\system\View\View.php(220): include('E:\\xampp\\htdocs...')
#2 E:\xampp\htdocs\PROJECTS\baidees\system\View\View.php(222): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#3 E:\xampp\htdocs\PROJECTS\baidees\system\Common.php(1217): CodeIgniter\View\View->render('admin/report/re...', Array, true)
#4 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Admin\Report.php(15): view('admin/report/re...', Array)
#5 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Report->index()
#6 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Report))
#7 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#8 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#9 {main}
CRITICAL - 2021-12-20 07:03:42 --> Invalid argument supplied for foreach()
#0 E:\xampp\htdocs\PROJECTS\baidees\app\Views\admin\report\reportlist.php(41): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Invalid argumen...', 'E:\\xampp\\htdocs...', 41, Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\system\View\View.php(220): include('E:\\xampp\\htdocs...')
#2 E:\xampp\htdocs\PROJECTS\baidees\system\View\View.php(222): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#3 E:\xampp\htdocs\PROJECTS\baidees\system\Common.php(1217): CodeIgniter\View\View->render('admin/report/re...', Array, true)
#4 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Admin\Report.php(15): view('admin/report/re...', Array)
#5 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Report->index()
#6 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Report))
#7 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#8 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#9 {main}
CRITICAL - 2021-12-20 07:03:42 --> Invalid argument supplied for foreach()
#0 E:\xampp\htdocs\PROJECTS\baidees\app\Views\admin\report\reportlist.php(41): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Invalid argumen...', 'E:\\xampp\\htdocs...', 41, Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\system\View\View.php(220): include('E:\\xampp\\htdocs...')
#2 E:\xampp\htdocs\PROJECTS\baidees\system\View\View.php(222): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#3 E:\xampp\htdocs\PROJECTS\baidees\system\Common.php(1217): CodeIgniter\View\View->render('admin/report/re...', Array, true)
#4 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Admin\Report.php(15): view('admin/report/re...', Array)
#5 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Report->index()
#6 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Report))
#7 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#8 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#9 {main}
CRITICAL - 2021-12-20 07:03:42 --> Invalid argument supplied for foreach()
#0 E:\xampp\htdocs\PROJECTS\baidees\app\Views\admin\report\reportlist.php(41): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Invalid argumen...', 'E:\\xampp\\htdocs...', 41, Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\system\View\View.php(220): include('E:\\xampp\\htdocs...')
#2 E:\xampp\htdocs\PROJECTS\baidees\system\View\View.php(222): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#3 E:\xampp\htdocs\PROJECTS\baidees\system\Common.php(1217): CodeIgniter\View\View->render('admin/report/re...', Array, true)
#4 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Admin\Report.php(15): view('admin/report/re...', Array)
#5 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Report->index()
#6 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Report))
#7 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#8 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#9 {main}
CRITICAL - 2021-12-20 07:03:42 --> Invalid argument supplied for foreach()
#0 E:\xampp\htdocs\PROJECTS\baidees\app\Views\admin\report\reportlist.php(41): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Invalid argumen...', 'E:\\xampp\\htdocs...', 41, Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\system\View\View.php(220): include('E:\\xampp\\htdocs...')
#2 E:\xampp\htdocs\PROJECTS\baidees\system\View\View.php(222): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#3 E:\xampp\htdocs\PROJECTS\baidees\system\Common.php(1217): CodeIgniter\View\View->render('admin/report/re...', Array, true)
#4 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Admin\Report.php(15): view('admin/report/re...', Array)
#5 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Report->index()
#6 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Report))
#7 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#8 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#9 {main}
CRITICAL - 2021-12-20 07:03:42 --> Invalid argument supplied for foreach()
#0 E:\xampp\htdocs\PROJECTS\baidees\app\Views\admin\report\reportlist.php(41): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Invalid argumen...', 'E:\\xampp\\htdocs...', 41, Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\system\View\View.php(220): include('E:\\xampp\\htdocs...')
#2 E:\xampp\htdocs\PROJECTS\baidees\system\View\View.php(222): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#3 E:\xampp\htdocs\PROJECTS\baidees\system\Common.php(1217): CodeIgniter\View\View->render('admin/report/re...', Array, true)
#4 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Admin\Report.php(15): view('admin/report/re...', Array)
#5 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Report->index()
#6 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Report))
#7 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#8 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#9 {main}
CRITICAL - 2021-12-20 07:03:42 --> Invalid argument supplied for foreach()
#0 E:\xampp\htdocs\PROJECTS\baidees\app\Views\admin\report\reportlist.php(41): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Invalid argumen...', 'E:\\xampp\\htdocs...', 41, Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\system\View\View.php(220): include('E:\\xampp\\htdocs...')
#2 E:\xampp\htdocs\PROJECTS\baidees\system\View\View.php(222): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#3 E:\xampp\htdocs\PROJECTS\baidees\system\Common.php(1217): CodeIgniter\View\View->render('admin/report/re...', Array, true)
#4 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Admin\Report.php(15): view('admin/report/re...', Array)
#5 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Report->index()
#6 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Report))
#7 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#8 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#9 {main}
CRITICAL - 2021-12-20 07:03:42 --> Invalid argument supplied for foreach()
#0 E:\xampp\htdocs\PROJECTS\baidees\app\Views\admin\report\reportlist.php(41): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Invalid argumen...', 'E:\\xampp\\htdocs...', 41, Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\system\View\View.php(220): include('E:\\xampp\\htdocs...')
#2 E:\xampp\htdocs\PROJECTS\baidees\system\View\View.php(222): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#3 E:\xampp\htdocs\PROJECTS\baidees\system\Common.php(1217): CodeIgniter\View\View->render('admin/report/re...', Array, true)
#4 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Admin\Report.php(15): view('admin/report/re...', Array)
#5 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Report->index()
#6 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Report))
#7 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#8 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#9 {main}
CRITICAL - 2021-12-20 07:03:42 --> Invalid argument supplied for foreach()
#0 E:\xampp\htdocs\PROJECTS\baidees\app\Views\admin\report\reportlist.php(41): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Invalid argumen...', 'E:\\xampp\\htdocs...', 41, Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\system\View\View.php(220): include('E:\\xampp\\htdocs...')
#2 E:\xampp\htdocs\PROJECTS\baidees\system\View\View.php(222): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#3 E:\xampp\htdocs\PROJECTS\baidees\system\Common.php(1217): CodeIgniter\View\View->render('admin/report/re...', Array, true)
#4 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Admin\Report.php(15): view('admin/report/re...', Array)
#5 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Report->index()
#6 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Report))
#7 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#8 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#9 {main}
CRITICAL - 2021-12-20 07:03:42 --> Invalid argument supplied for foreach()
#0 E:\xampp\htdocs\PROJECTS\baidees\app\Views\admin\report\reportlist.php(41): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Invalid argumen...', 'E:\\xampp\\htdocs...', 41, Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\system\View\View.php(220): include('E:\\xampp\\htdocs...')
#2 E:\xampp\htdocs\PROJECTS\baidees\system\View\View.php(222): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#3 E:\xampp\htdocs\PROJECTS\baidees\system\Common.php(1217): CodeIgniter\View\View->render('admin/report/re...', Array, true)
#4 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Admin\Report.php(15): view('admin/report/re...', Array)
#5 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Report->index()
#6 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Report))
#7 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#8 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#9 {main}
CRITICAL - 2021-12-20 07:03:42 --> Invalid argument supplied for foreach()
#0 E:\xampp\htdocs\PROJECTS\baidees\app\Views\admin\report\reportlist.php(41): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Invalid argumen...', 'E:\\xampp\\htdocs...', 41, Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\system\View\View.php(220): include('E:\\xampp\\htdocs...')
#2 E:\xampp\htdocs\PROJECTS\baidees\system\View\View.php(222): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#3 E:\xampp\htdocs\PROJECTS\baidees\system\Common.php(1217): CodeIgniter\View\View->render('admin/report/re...', Array, true)
#4 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Admin\Report.php(15): view('admin/report/re...', Array)
#5 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Report->index()
#6 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Report))
#7 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#8 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#9 {main}
CRITICAL - 2021-12-20 07:03:42 --> Invalid argument supplied for foreach()
#0 E:\xampp\htdocs\PROJECTS\baidees\app\Views\admin\report\reportlist.php(41): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Invalid argumen...', 'E:\\xampp\\htdocs...', 41, Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\system\View\View.php(220): include('E:\\xampp\\htdocs...')
#2 E:\xampp\htdocs\PROJECTS\baidees\system\View\View.php(222): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#3 E:\xampp\htdocs\PROJECTS\baidees\system\Common.php(1217): CodeIgniter\View\View->render('admin/report/re...', Array, true)
#4 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Admin\Report.php(15): view('admin/report/re...', Array)
#5 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Report->index()
#6 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Report))
#7 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#8 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#9 {main}
CRITICAL - 2021-12-20 07:03:42 --> Invalid argument supplied for foreach()
#0 E:\xampp\htdocs\PROJECTS\baidees\app\Views\admin\report\reportlist.php(41): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Invalid argumen...', 'E:\\xampp\\htdocs...', 41, Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\system\View\View.php(220): include('E:\\xampp\\htdocs...')
#2 E:\xampp\htdocs\PROJECTS\baidees\system\View\View.php(222): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#3 E:\xampp\htdocs\PROJECTS\baidees\system\Common.php(1217): CodeIgniter\View\View->render('admin/report/re...', Array, true)
#4 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Admin\Report.php(15): view('admin/report/re...', Array)
#5 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Report->index()
#6 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Report))
#7 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#8 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#9 {main}
CRITICAL - 2021-12-20 07:03:42 --> Invalid argument supplied for foreach()
#0 E:\xampp\htdocs\PROJECTS\baidees\app\Views\admin\report\reportlist.php(41): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Invalid argumen...', 'E:\\xampp\\htdocs...', 41, Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\system\View\View.php(220): include('E:\\xampp\\htdocs...')
#2 E:\xampp\htdocs\PROJECTS\baidees\system\View\View.php(222): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#3 E:\xampp\htdocs\PROJECTS\baidees\system\Common.php(1217): CodeIgniter\View\View->render('admin/report/re...', Array, true)
#4 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Admin\Report.php(15): view('admin/report/re...', Array)
#5 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Report->index()
#6 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Report))
#7 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#8 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#9 {main}
CRITICAL - 2021-12-20 07:03:43 --> Invalid argument supplied for foreach()
#0 E:\xampp\htdocs\PROJECTS\baidees\app\Views\admin\report\reportlist.php(41): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Invalid argumen...', 'E:\\xampp\\htdocs...', 41, Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\system\View\View.php(220): include('E:\\xampp\\htdocs...')
#2 E:\xampp\htdocs\PROJECTS\baidees\system\View\View.php(222): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#3 E:\xampp\htdocs\PROJECTS\baidees\system\Common.php(1217): CodeIgniter\View\View->render('admin/report/re...', Array, true)
#4 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Admin\Report.php(15): view('admin/report/re...', Array)
#5 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Report->index()
#6 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Report))
#7 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#8 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#9 {main}
CRITICAL - 2021-12-20 07:04:49 --> Invalid argument supplied for foreach()
#0 E:\xampp\htdocs\PROJECTS\baidees\app\Views\admin\report\reportlist.php(143): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Invalid argumen...', 'E:\\xampp\\htdocs...', 143, Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\system\View\View.php(220): include('E:\\xampp\\htdocs...')
#2 E:\xampp\htdocs\PROJECTS\baidees\system\View\View.php(222): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#3 E:\xampp\htdocs\PROJECTS\baidees\system\Common.php(1217): CodeIgniter\View\View->render('admin/report/re...', Array, true)
#4 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Admin\Report.php(15): view('admin/report/re...', Array)
#5 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Report->index()
#6 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Report))
#7 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#8 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#9 {main}
CRITICAL - 2021-12-20 07:05:14 --> Invalid argument supplied for foreach()
#0 E:\xampp\htdocs\PROJECTS\baidees\app\Views\admin\report\reportlist.php(143): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Invalid argumen...', 'E:\\xampp\\htdocs...', 143, Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\system\View\View.php(220): include('E:\\xampp\\htdocs...')
#2 E:\xampp\htdocs\PROJECTS\baidees\system\View\View.php(222): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#3 E:\xampp\htdocs\PROJECTS\baidees\system\Common.php(1217): CodeIgniter\View\View->render('admin/report/re...', Array, true)
#4 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Admin\Report.php(15): view('admin/report/re...', Array)
#5 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Report->index()
#6 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Report))
#7 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#8 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#9 {main}
CRITICAL - 2021-12-20 07:05:15 --> Invalid argument supplied for foreach()
#0 E:\xampp\htdocs\PROJECTS\baidees\app\Views\admin\report\reportlist.php(143): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Invalid argumen...', 'E:\\xampp\\htdocs...', 143, Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\system\View\View.php(220): include('E:\\xampp\\htdocs...')
#2 E:\xampp\htdocs\PROJECTS\baidees\system\View\View.php(222): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#3 E:\xampp\htdocs\PROJECTS\baidees\system\Common.php(1217): CodeIgniter\View\View->render('admin/report/re...', Array, true)
#4 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Admin\Report.php(15): view('admin/report/re...', Array)
#5 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Report->index()
#6 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Report))
#7 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#8 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#9 {main}
CRITICAL - 2021-12-20 07:05:15 --> Invalid argument supplied for foreach()
#0 E:\xampp\htdocs\PROJECTS\baidees\app\Views\admin\report\reportlist.php(143): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Invalid argumen...', 'E:\\xampp\\htdocs...', 143, Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\system\View\View.php(220): include('E:\\xampp\\htdocs...')
#2 E:\xampp\htdocs\PROJECTS\baidees\system\View\View.php(222): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#3 E:\xampp\htdocs\PROJECTS\baidees\system\Common.php(1217): CodeIgniter\View\View->render('admin/report/re...', Array, true)
#4 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Admin\Report.php(15): view('admin/report/re...', Array)
#5 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Report->index()
#6 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Report))
#7 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#8 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#9 {main}
CRITICAL - 2021-12-20 07:05:15 --> Invalid argument supplied for foreach()
#0 E:\xampp\htdocs\PROJECTS\baidees\app\Views\admin\report\reportlist.php(143): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Invalid argumen...', 'E:\\xampp\\htdocs...', 143, Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\system\View\View.php(220): include('E:\\xampp\\htdocs...')
#2 E:\xampp\htdocs\PROJECTS\baidees\system\View\View.php(222): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#3 E:\xampp\htdocs\PROJECTS\baidees\system\Common.php(1217): CodeIgniter\View\View->render('admin/report/re...', Array, true)
#4 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Admin\Report.php(15): view('admin/report/re...', Array)
#5 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Report->index()
#6 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Report))
#7 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#8 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#9 {main}
