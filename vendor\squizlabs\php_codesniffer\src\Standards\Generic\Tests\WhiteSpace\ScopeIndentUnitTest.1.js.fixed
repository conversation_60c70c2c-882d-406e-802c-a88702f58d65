phpcs:set Generic.WhiteSpace.ScopeIndent tabIndent false
var script = document.createElement('script');
script.onload = function()
{
        clearTimeout(t);
    script456.onload = null;
    script.onreadystatechange = null;
    callback.call(this);

};

this.callbacks[type] = {
    namespaces: {},
    others: []
};

blah = function()
{
    print something;

}

test(blah, function() {
    print something;
});

var test = [{x: 10}];
var test = [{
    x: 10,
    y: {
        b14h: 12,
        'b14h': 12
    },
    z: 23
}];

Viper.prototype = {

    _removeEvents: function(elem)
     {
        if (!elem) {
            elem = this.element;
        }

        ViperUtil.removeEvent(elem, '.' + this.getEventNamespace());

    }

};

this.init = function(data) {
    if (_pageListWdgt) {
        GUI.getWidget('changedPagesList').addItemClickedCallback(
            function(itemid, target) {
                draftChangeTypeClicked(
                    itemid,
                    target,
                    {
                        reviewData: _reviewData,
                        pageid: itemid
                    }
                );
            }
        );
    }//end if

};

a(
    function() {
        var _a = function() {
            b = false;

        };
        true
    }
);

(function() {
    a = function() {
        a(function() {
            if (true) {
                a = true;
            }
        });

        a(
            function() {
                if (true) {
                    if (true) {
                        a = true;
                    }
                }
            }
        );

        a(
            function() {
                if (true) {
                    a = true;
                }
            }
        );

    };

})();

a.prototype = {

    a: function()
    {
        var currentSize = null;
        ViperUtil.addEvent(
            header,
            'safedblclick',
            function() {},
        );

        if (topContent) {
            ViperUtil.addClass(topContent, 'Viper-popup-top');
            main.appendChild(topContent);
        }

        ViperUtil.addClass(midContent, 'Viper-popup-content');
        main.appendChild(midContent);
    }

};

a.prototype = {

    a: function()
    {
        ViperUtil.addClass(midContent, 'Viper-popup-content');
        main.appendChild(midContent);

        var mouseUpAction  = function() {};
        var preventMouseUp = false;
        var self           = this;
        if (clickAction) {
        }
    }

};

a.prototype = {

    a: function()
    {
        var a = function() {
            var a = 'foo';
        };

        if (true) {
        }

    },

    b: function()
    {
        ViperUtil.addEvent(
            function() {
                if (fullScreen !== true) {
                    currentSize = {
                    };

                    showfullScreen();
                }
            }
        );

    },

    c: function()
    {
        this.a(
            {
                a: function() {
                    form.onsubmit = function() {
                        return false;
                    };

                    var a = true;
                }
            }
        );

    }

};

a.prototype = {
    init: function()
    {},

    _b: function()
    {
    }

};

for (var i = 0; i < 10; i++) {
    var foo = {foo:{'a':'b',
        'c':'d'}};
}

class TestOk
{
    destroy()
    {
        setTimeout(a, 1000);

        if (typeof self.callbackOnClose === "function") {
            self.callbackOnClose();
        }
    }
}

class TestBad
{
    destroy()
    {
        setTimeout(function () {
            return;
        }, 1000);

        if (typeof self.callbackOnClose === "function") {
            self.callbackOnClose();
        }
    }
}

( function( $ ) {
    foo(function( value ) {
            value.bind( function( newval ) {
                    $( '#bar' ).html( newval );
            } );
    } )( jQuery );
