ERROR - 2022-02-21 06:17:37 --> mysqli_sql_exception: Column 'mobile' cannot be null in /home1/hitsicik/thebaidees.com/system/Database/MySQLi/Connection.php:314
Stack trace:
#0 /home1/hitsicik/thebaidees.com/system/Database/MySQLi/Connection.php(314): mysqli->query('INSERT INTO `tb...')
#1 /home1/hitsicik/thebaidees.com/system/Database/BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `tb...')
#2 /home1/hitsicik/thebaidees.com/system/Database/BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `tb...')
#3 /home1/hitsicik/thebaidees.com/system/Database/BaseBuilder.php(2263): CodeIgniter\Database\BaseConnection->query('INSERT INTO `tb...', Array, false)
#4 /home1/hitsicik/thebaidees.com/app/Models/FrontendModel.php(301): CodeIgniter\Database\BaseBuilder->insert(Array)
#5 /home1/hitsicik/thebaidees.com/app/Controllers/Frontend.php(965): App\Models\FrontendModel->insertCustomerDetails(Array)
#6 /home1/hitsicik/thebaidees.com/system/CodeIgniter.php(936): App\Controllers\Frontend->enterotp()
#7 /home1/hitsicik/thebaidees.com/system/CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 /home1/hitsicik/thebaidees.com/system/CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 /home1/hitsicik/thebaidees.com/index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2022-02-21 06:19:41 --> mysqli_sql_exception: Column 'mobile' cannot be null in /home1/hitsicik/thebaidees.com/system/Database/MySQLi/Connection.php:314
Stack trace:
#0 /home1/hitsicik/thebaidees.com/system/Database/MySQLi/Connection.php(314): mysqli->query('INSERT INTO `tb...')
#1 /home1/hitsicik/thebaidees.com/system/Database/BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `tb...')
#2 /home1/hitsicik/thebaidees.com/system/Database/BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `tb...')
#3 /home1/hitsicik/thebaidees.com/system/Database/BaseBuilder.php(2263): CodeIgniter\Database\BaseConnection->query('INSERT INTO `tb...', Array, false)
#4 /home1/hitsicik/thebaidees.com/app/Models/FrontendModel.php(301): CodeIgniter\Database\BaseBuilder->insert(Array)
#5 /home1/hitsicik/thebaidees.com/app/Controllers/Frontend.php(965): App\Models\FrontendModel->insertCustomerDetails(Array)
#6 /home1/hitsicik/thebaidees.com/system/CodeIgniter.php(936): App\Controllers\Frontend->enterotp()
#7 /home1/hitsicik/thebaidees.com/system/CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 /home1/hitsicik/thebaidees.com/system/CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 /home1/hitsicik/thebaidees.com/index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2022-02-21 06:21:14 --> mysqli_sql_exception: Column 'mobile' cannot be null in /home1/hitsicik/thebaidees.com/system/Database/MySQLi/Connection.php:314
Stack trace:
#0 /home1/hitsicik/thebaidees.com/system/Database/MySQLi/Connection.php(314): mysqli->query('INSERT INTO `tb...')
#1 /home1/hitsicik/thebaidees.com/system/Database/BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `tb...')
#2 /home1/hitsicik/thebaidees.com/system/Database/BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `tb...')
#3 /home1/hitsicik/thebaidees.com/system/Database/BaseBuilder.php(2263): CodeIgniter\Database\BaseConnection->query('INSERT INTO `tb...', Array, false)
#4 /home1/hitsicik/thebaidees.com/app/Models/FrontendModel.php(301): CodeIgniter\Database\BaseBuilder->insert(Array)
#5 /home1/hitsicik/thebaidees.com/app/Controllers/Frontend.php(965): App\Models\FrontendModel->insertCustomerDetails(Array)
#6 /home1/hitsicik/thebaidees.com/system/CodeIgniter.php(936): App\Controllers\Frontend->enterotp()
#7 /home1/hitsicik/thebaidees.com/system/CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 /home1/hitsicik/thebaidees.com/system/CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 /home1/hitsicik/thebaidees.com/index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2022-02-21 06:24:34 --> mysqli_sql_exception: Column 'email' cannot be null in /home1/hitsicik/thebaidees.com/system/Database/MySQLi/Connection.php:314
Stack trace:
#0 /home1/hitsicik/thebaidees.com/system/Database/MySQLi/Connection.php(314): mysqli->query('INSERT INTO `tb...')
#1 /home1/hitsicik/thebaidees.com/system/Database/BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `tb...')
#2 /home1/hitsicik/thebaidees.com/system/Database/BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `tb...')
#3 /home1/hitsicik/thebaidees.com/system/Database/BaseBuilder.php(2263): CodeIgniter\Database\BaseConnection->query('INSERT INTO `tb...', Array, false)
#4 /home1/hitsicik/thebaidees.com/app/Models/FrontendModel.php(313): CodeIgniter\Database\BaseBuilder->insert(Array)
#5 /home1/hitsicik/thebaidees.com/app/Controllers/Frontend.php(969): App\Models\FrontendModel->insertOrderDetails(Array)
#6 /home1/hitsicik/thebaidees.com/system/CodeIgniter.php(936): App\Controllers\Frontend->enterotp()
#7 /home1/hitsicik/thebaidees.com/system/CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 /home1/hitsicik/thebaidees.com/system/CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 /home1/hitsicik/thebaidees.com/index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2022-02-21 06:35:14 --> mysqli_sql_exception: Column 'email' cannot be null in /home1/hitsicik/thebaidees.com/system/Database/MySQLi/Connection.php:314
Stack trace:
#0 /home1/hitsicik/thebaidees.com/system/Database/MySQLi/Connection.php(314): mysqli->query('INSERT INTO `tb...')
#1 /home1/hitsicik/thebaidees.com/system/Database/BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `tb...')
#2 /home1/hitsicik/thebaidees.com/system/Database/BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `tb...')
#3 /home1/hitsicik/thebaidees.com/system/Database/BaseBuilder.php(2263): CodeIgniter\Database\BaseConnection->query('INSERT INTO `tb...', Array, false)
#4 /home1/hitsicik/thebaidees.com/app/Models/FrontendModel.php(313): CodeIgniter\Database\BaseBuilder->insert(Array)
#5 /home1/hitsicik/thebaidees.com/app/Controllers/Frontend.php(969): App\Models\FrontendModel->insertOrderDetails(Array)
#6 /home1/hitsicik/thebaidees.com/system/CodeIgniter.php(936): App\Controllers\Frontend->enterotp()
#7 /home1/hitsicik/thebaidees.com/system/CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 /home1/hitsicik/thebaidees.com/system/CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 /home1/hitsicik/thebaidees.com/index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2022-02-21 06:37:58 --> mysqli_sql_exception: Column 'email' cannot be null in /home1/hitsicik/thebaidees.com/system/Database/MySQLi/Connection.php:314
Stack trace:
#0 /home1/hitsicik/thebaidees.com/system/Database/MySQLi/Connection.php(314): mysqli->query('INSERT INTO `tb...')
#1 /home1/hitsicik/thebaidees.com/system/Database/BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `tb...')
#2 /home1/hitsicik/thebaidees.com/system/Database/BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `tb...')
#3 /home1/hitsicik/thebaidees.com/system/Database/BaseBuilder.php(2263): CodeIgniter\Database\BaseConnection->query('INSERT INTO `tb...', Array, false)
#4 /home1/hitsicik/thebaidees.com/app/Models/FrontendModel.php(313): CodeIgniter\Database\BaseBuilder->insert(Array)
#5 /home1/hitsicik/thebaidees.com/app/Controllers/Frontend.php(969): App\Models\FrontendModel->insertOrderDetails(Array)
#6 /home1/hitsicik/thebaidees.com/system/CodeIgniter.php(936): App\Controllers\Frontend->enterotp()
#7 /home1/hitsicik/thebaidees.com/system/CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 /home1/hitsicik/thebaidees.com/system/CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 /home1/hitsicik/thebaidees.com/index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2022-02-21 06:54:51 --> mysqli_sql_exception: Column 'email' cannot be null in /home1/hitsicik/thebaidees.com/system/Database/MySQLi/Connection.php:314
Stack trace:
#0 /home1/hitsicik/thebaidees.com/system/Database/MySQLi/Connection.php(314): mysqli->query('INSERT INTO `tb...')
#1 /home1/hitsicik/thebaidees.com/system/Database/BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `tb...')
#2 /home1/hitsicik/thebaidees.com/system/Database/BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `tb...')
#3 /home1/hitsicik/thebaidees.com/system/Database/BaseBuilder.php(2263): CodeIgniter\Database\BaseConnection->query('INSERT INTO `tb...', Array, false)
#4 /home1/hitsicik/thebaidees.com/app/Models/FrontendModel.php(313): CodeIgniter\Database\BaseBuilder->insert(Array)
#5 /home1/hitsicik/thebaidees.com/app/Controllers/Frontend.php(969): App\Models\FrontendModel->insertOrderDetails(Array)
#6 /home1/hitsicik/thebaidees.com/system/CodeIgniter.php(936): App\Controllers\Frontend->enterotp()
#7 /home1/hitsicik/thebaidees.com/system/CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 /home1/hitsicik/thebaidees.com/system/CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 /home1/hitsicik/thebaidees.com/index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2022-02-21 07:20:40 --> mysqli_sql_exception: Column 'email' cannot be null in /home1/hitsicik/thebaidees.com/system/Database/MySQLi/Connection.php:314
Stack trace:
#0 /home1/hitsicik/thebaidees.com/system/Database/MySQLi/Connection.php(314): mysqli->query('INSERT INTO `tb...')
#1 /home1/hitsicik/thebaidees.com/system/Database/BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `tb...')
#2 /home1/hitsicik/thebaidees.com/system/Database/BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `tb...')
#3 /home1/hitsicik/thebaidees.com/system/Database/BaseBuilder.php(2263): CodeIgniter\Database\BaseConnection->query('INSERT INTO `tb...', Array, false)
#4 /home1/hitsicik/thebaidees.com/app/Models/FrontendModel.php(313): CodeIgniter\Database\BaseBuilder->insert(Array)
#5 /home1/hitsicik/thebaidees.com/app/Controllers/Frontend.php(969): App\Models\FrontendModel->insertOrderDetails(Array)
#6 /home1/hitsicik/thebaidees.com/system/CodeIgniter.php(936): App\Controllers\Frontend->enterotp()
#7 /home1/hitsicik/thebaidees.com/system/CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 /home1/hitsicik/thebaidees.com/system/CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 /home1/hitsicik/thebaidees.com/index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2022-02-21 07:25:04 --> mysqli_sql_exception: Column 'email' cannot be null in /home1/hitsicik/thebaidees.com/system/Database/MySQLi/Connection.php:314
Stack trace:
#0 /home1/hitsicik/thebaidees.com/system/Database/MySQLi/Connection.php(314): mysqli->query('INSERT INTO `tb...')
#1 /home1/hitsicik/thebaidees.com/system/Database/BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `tb...')
#2 /home1/hitsicik/thebaidees.com/system/Database/BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `tb...')
#3 /home1/hitsicik/thebaidees.com/system/Database/BaseBuilder.php(2263): CodeIgniter\Database\BaseConnection->query('INSERT INTO `tb...', Array, false)
#4 /home1/hitsicik/thebaidees.com/app/Models/FrontendModel.php(313): CodeIgniter\Database\BaseBuilder->insert(Array)
#5 /home1/hitsicik/thebaidees.com/app/Controllers/Frontend.php(969): App\Models\FrontendModel->insertOrderDetails(Array)
#6 /home1/hitsicik/thebaidees.com/system/CodeIgniter.php(936): App\Controllers\Frontend->enterotp()
#7 /home1/hitsicik/thebaidees.com/system/CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 /home1/hitsicik/thebaidees.com/system/CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 /home1/hitsicik/thebaidees.com/index.php(48): CodeIgniter\CodeIgniter->run()
#10 {main}
