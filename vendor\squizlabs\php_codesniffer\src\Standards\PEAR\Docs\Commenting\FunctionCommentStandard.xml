<documentation title="Function Comments">
    <standard>
    <![CDATA[
    Functions must have a non-empty doc comment.  The short description must be on the second line of the comment.  Each description must have one blank comment line before and after.  There must be one blank line before the tags in the comments.  There must be a tag for each of the parameters in the right order with the right variable names with a comment.  There must be a return tag.  Any throw tag must have an exception class.
    ]]>
    </standard>
    <code_comparison>
        <code title="Valid: A function doc comment is used.">
        <![CDATA[
<em>/**
 * Short description here.
 *
 * @return void
 */</em>
 function foo()
 {
 }
        ]]>
        </code>
        <code title="Invalid: No doc comment for the function.">
        <![CDATA[
 function foo()
 {
 }
        ]]>
        </code>
    </code_comparison>
    <code_comparison>
        <code title="Valid: Short description is the second line of the comment.">
        <![CDATA[
/**
 * <em>Short description here.</em>
 *
 * @return void
 */
 function foo()
 {
 }
        ]]>
        </code>
        <code title="Invalid: An extra blank line before the short description.">
        <![CDATA[
/**
 * <em></em>
 * <em>Short description here.</em>
 *
 * @return void
 */
 function foo()
 {
 }
        ]]>
        </code>
    </code_comparison>
    <code_comparison>
        <code title="Valid: Exactly one blank line around descriptions.">
        <![CDATA[
/**
 * Short description here.
 * <em></em>
 * Long description here.
 * <em></em>
 * @return void
 */
 function foo()
 {
 }
        ]]>
        </code>
        <code title="Invalid: Extra blank lines around the descriptions.">
        <![CDATA[
/**
 * Short description here.
 * <em></em>
 * <em></em>
 * Long description here.
 * <em></em>
 * <em></em>
 * @return void
 */
 function foo()
 {
 }
        ]]>
        </code>
    </code_comparison>
    <code_comparison>
        <code title="Valid: Exactly one blank line before the tags.">
        <![CDATA[
/**
 * Short description here.
 *
 * Long description here.
 * <em></em>
 * @return void
 */
 function foo()
 {
 }
        ]]>
        </code>
        <code title="Invalid: Extra blank lines before the tags.">
        <![CDATA[
/**
 * Short description here.
 *
 * Long description here.
 * <em></em>
 * <em></em>
 * @return void
 */
 function foo()
 {
 }
        ]]>
        </code>
    </code_comparison>
    <code_comparison>
        <code title="Valid: Throws tag has an exception class.">
        <![CDATA[
/**
 * Short description here.
 *
 * @return void
 * @throws <em>FooException</em>
 */
 function foo()
 {
 }
        ]]>
        </code>
        <code title="Invalid: No exception class given for throws tag.">
        <![CDATA[
/**
 * Short description here.
 *
 * @return void
 * <em>@throws</em>
 */
 function foo()
 {
 }
        ]]>
        </code>
    </code_comparison>
    <code_comparison>
        <code title="Valid: Return tag present.">
        <![CDATA[
/**
 * Short description here.
 *
 * <em>@return void</em>
 */
 function foo()
 {
 }
        ]]>
        </code>
        <code title="Invalid: No return tag.">
        <![CDATA[
/**
 * Short description here.
 */
 function foo()
 {
 }
        ]]>
        </code>
    </code_comparison>
    <code_comparison>
        <code title="Valid: Param names are correct.">
        <![CDATA[
/**
 * Short description here.
 *
 * @param string <em>$foo</em> Foo parameter
 * @param string <em>$bar</em> Bar parameter
 * @return void
 */
 function foo(<em>$foo</em>, <em>$bar</em>)
 {
 }
        ]]>
        </code>
        <code title="Invalid: Wrong parameter name doesn't match function signature.">
        <![CDATA[
/**
 * Short description here.
 *
 * @param string $foo Foo parameter
 * @param string <em>$qux</em> Bar parameter
 * @return void
 */
 function foo($foo, <em>$bar</em>)
 {
 }
        ]]>
        </code>
    </code_comparison>
    <code_comparison>
        <code title="Valid: Param names are ordered correctly.">
        <![CDATA[
/**
 * Short description here.
 *
 * @param string <em>$foo</em> Foo parameter
 * @param string <em>$bar</em> Bar parameter
 * @return void
 */
 function foo(<em>$foo</em>, <em>$bar</em>)
 {
 }
        ]]>
        </code>
        <code title="Invalid: Wrong parameter order.">
        <![CDATA[
/**
 * Short description here.
 *
 * @param string <em>$bar</em> Bar parameter
 * @param string <em>$foo</em> Foo parameter
 * @return void
 */
 function foo(<em>$foo</em>, <em>$bar</em>)
 {
 }
        ]]>
        </code>
    </code_comparison>
</documentation>
