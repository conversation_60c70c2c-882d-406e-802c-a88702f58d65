CRITICAL - 2021-12-21 00:37:37 --> Cannot use object of type stdClass as array
#0 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Report->exportreport()
#1 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Report))
#2 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#3 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#4 {main}
CRITICAL - 2021-12-21 00:42:37 --> Cannot use object of type stdClass as array
#0 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Report->exportreport()
#1 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Report))
#2 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#3 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#4 {main}
CRITICAL - 2021-12-21 00:44:39 --> Cannot use object of type stdClass as array
#0 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Report->exportreport()
#1 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Report))
#2 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#3 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#4 {main}
CRITICAL - 2021-12-21 00:47:18 --> Cannot use object of type stdClass as array
#0 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Report->exportreport()
#1 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Report))
#2 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#3 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#4 {main}
CRITICAL - 2021-12-21 00:53:42 --> Cannot use object of type stdClass as array
#0 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Report->exportreport()
#1 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Report))
#2 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#3 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#4 {main}
CRITICAL - 2021-12-21 00:56:09 --> Cannot use object of type stdClass as array
#0 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Report->exportreport()
#1 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Report))
#2 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#3 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#4 {main}
CRITICAL - 2021-12-21 00:57:19 --> Cannot use object of type stdClass as array
#0 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Report->exportreport()
#1 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Report))
#2 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#3 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#4 {main}
CRITICAL - 2021-12-21 01:55:26 --> Use of undefined constant product_id - assumed 'product_id' (this will throw an Error in a future version of PHP)
#0 E:\xampp\htdocs\PROJECTS\baidees\app\Models\SalesentryModel.php(84): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Use of undefine...', 'E:\\xampp\\htdocs...', 84, Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Admin\Salesentry.php(216): App\Models\SalesentryModel->getProdNameById('8')
#2 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Salesentry->savesalesentry()
#3 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Salesentry))
#4 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#5 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#6 {main}
CRITICAL - 2021-12-21 01:57:05 --> Use of undefined constant product_id - assumed 'product_id' (this will throw an Error in a future version of PHP)
#0 E:\xampp\htdocs\PROJECTS\baidees\app\Models\SalesentryModel.php(84): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Use of undefine...', 'E:\\xampp\\htdocs...', 84, Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Admin\Salesentry.php(216): App\Models\SalesentryModel->getProdNameById('8')
#2 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Salesentry->savesalesentry()
#3 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Salesentry))
#4 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#5 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#6 {main}
CRITICAL - 2021-12-21 01:57:57 --> Call to undefined method CodeIgniter\Database\MySQLi\Builder::where_in()
#0 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Admin\Salesentry.php(216): App\Models\SalesentryModel->getProdNameById('8')
#1 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Salesentry->savesalesentry()
#2 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Salesentry))
#3 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#4 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#5 {main}
CRITICAL - 2021-12-21 01:59:02 --> Use of undefined constant product_id - assumed 'product_id' (this will throw an Error in a future version of PHP)
#0 E:\xampp\htdocs\PROJECTS\baidees\app\Models\SalesentryModel.php(84): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Use of undefine...', 'E:\\xampp\\htdocs...', 84, Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Admin\Salesentry.php(216): App\Models\SalesentryModel->getProdNameById('8')
#2 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Admin\Salesentry->savesalesentry()
#3 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Salesentry))
#4 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#5 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#6 {main}
