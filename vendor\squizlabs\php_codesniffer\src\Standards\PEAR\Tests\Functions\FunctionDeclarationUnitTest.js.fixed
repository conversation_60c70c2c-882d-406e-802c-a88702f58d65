
function someFunctionWithAVeryLongName(firstParameter='something',
    secondParameter='booooo', third=null, fourthParameter=false,
    fifthParameter=123.12, sixthParam=true
) {
}

function someFunctionWithAVeryLongName2(firstParameter='something',
    secondParameter='booooo', third=null, fourthParameter=false,
    fifthParameter=123.12, sixthParam=true
) {
}

function blah()
{
}

function blah()
{
}

var object =
{

    someFunctionWithAVeryLongName: function (firstParameter='something',
        secondParameter='booooo', third=null, fourthParameter=false,
        fifthParameter=123.12, sixthParam=true
    ) /** w00t */ {
    }

    someFunctionWithAVeryLongName2: function (
        firstParameter='something', secondParameter='booooo', third=null
    ) {
    }

}

function getInstalledStandards(
    includeGeneric=false,
    standardsDir=''
) {
}

var a = Function('return 1+1');

class test
{
    myFunction()
    {
       return false;
    }

    myFunction2()
    {
       return false;
    }
}

( function ( $ ) {
    foo(function ( value ) {} )( jQuery );
