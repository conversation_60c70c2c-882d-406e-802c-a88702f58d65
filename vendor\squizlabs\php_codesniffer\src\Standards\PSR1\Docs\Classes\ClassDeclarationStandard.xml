<documentation title="Class Declaration">
    <standard>
    <![CDATA[
    Each class must be in a file by itself and must be under a namespace (a top-level vendor name).
    ]]>
    </standard>
    <code_comparison>
        <code title="Valid: One class in a file.">
        <![CDATA[
<?php
namespace Foo;

<em>class Bar</em> {
}
        ]]>
        </code>
        <code title="Invalid: Multiple classes in a single file.">
        <![CDATA[
<?php
namespace Foo;

<em>class Bar</em> {
}

<em>class Baz</em> {
}
        ]]>
        </code>
    </code_comparison>
    <code_comparison>
        <code title="Valid: A vendor-level namespace is used.">
        <![CDATA[
<?php
<em>namespace Foo;</em>

class Bar {
}
        ]]>
        </code>
        <code title="Invalid: No namespace used in file.">
        <![CDATA[
<?php
class Bar {
}
        ]]>
        </code>
    </code_comparison>
</documentation>
