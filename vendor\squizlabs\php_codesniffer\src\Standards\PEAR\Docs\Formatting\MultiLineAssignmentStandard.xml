<documentation title="Multi-Line Assignment">
    <standard>
    <![CDATA[
    Multi-line assignment should have the equals sign be the first item on the second line indented correctly.
    ]]>
    </standard>
    <code_comparison>
        <code title="Valid: Assignment operator at the start of the second line.">
        <![CDATA[
$foo
    <em>=</em> $bar;
        ]]>
        </code>
        <code title="Invalid: Assignment operator at end of first line.">
        <![CDATA[
$foo <em>=</em>
    $bar;
        ]]>
        </code>
    </code_comparison>
    <code_comparison>
        <code title="Valid: Assignment operator indented one level.">
        <![CDATA[
$foo
<em>    </em>= $bar;
        ]]>
        </code>
        <code title="Invalid: Assignment operator not indented.">
        <![CDATA[
$foo
<em></em>= $bar;
        ]]>
        </code>
    </code_comparison>
</documentation>
