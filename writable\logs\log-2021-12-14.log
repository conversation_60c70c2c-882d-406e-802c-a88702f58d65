ERROR - 2021-12-14 01:02:15 --> mysqli_sql_exception: Column 'mobile' cannot be null in E:\xampp\htdocs\PROJECTS\baidees\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\xampp\htdocs\PROJECTS\baidees\system\Database\MySQLi\Connection.php(314): mysqli->query('INSERT INTO `tb...')
#1 E:\xampp\htdocs\PROJECTS\baidees\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `tb...')
#2 E:\xampp\htdocs\PROJECTS\baidees\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `tb...')
#3 E:\xampp\htdocs\PROJECTS\baidees\system\Database\BaseBuilder.php(2263): CodeIgniter\Database\BaseConnection->query('INSERT INTO `tb...', Array, false)
#4 E:\xampp\htdocs\PROJECTS\baidees\app\Models\FrontendModel.php(235): CodeIgniter\Database\BaseBuilder->insert(Array)
#5 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Frontend.php(607): App\Models\FrontendModel->insertCustomerDetails(Array)
#6 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Frontend->enterotp()
#7 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2021-12-14 01:05:10 --> print_r() expects at least 1 parameter, 0 given
#0 [internal function]: CodeIgniter\Debug\Exceptions->errorHandler(2, 'print_r() expec...', 'E:\\xampp\\htdocs...', 657, Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Frontend.php(657): print_r()
#2 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Frontend->enterotp()
#3 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#4 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#5 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#6 {main}
ERROR - 2021-12-14 01:14:12 --> mysqli_sql_exception: Column 'email' cannot be null in E:\xampp\htdocs\PROJECTS\baidees\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\xampp\htdocs\PROJECTS\baidees\system\Database\MySQLi\Connection.php(314): mysqli->query('INSERT INTO `tb...')
#1 E:\xampp\htdocs\PROJECTS\baidees\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `tb...')
#2 E:\xampp\htdocs\PROJECTS\baidees\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `tb...')
#3 E:\xampp\htdocs\PROJECTS\baidees\system\Database\BaseBuilder.php(2263): CodeIgniter\Database\BaseConnection->query('INSERT INTO `tb...', Array, false)
#4 E:\xampp\htdocs\PROJECTS\baidees\app\Models\FrontendModel.php(247): CodeIgniter\Database\BaseBuilder->insert(Array)
#5 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Frontend.php(611): App\Models\FrontendModel->insertOrderDetails(Array)
#6 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Frontend->enterotp()
#7 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2021-12-14 01:14:39 --> mysqli_sql_exception: Column 'email' cannot be null in E:\xampp\htdocs\PROJECTS\baidees\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\xampp\htdocs\PROJECTS\baidees\system\Database\MySQLi\Connection.php(314): mysqli->query('INSERT INTO `tb...')
#1 E:\xampp\htdocs\PROJECTS\baidees\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `tb...')
#2 E:\xampp\htdocs\PROJECTS\baidees\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `tb...')
#3 E:\xampp\htdocs\PROJECTS\baidees\system\Database\BaseBuilder.php(2263): CodeIgniter\Database\BaseConnection->query('INSERT INTO `tb...', Array, false)
#4 E:\xampp\htdocs\PROJECTS\baidees\app\Models\FrontendModel.php(247): CodeIgniter\Database\BaseBuilder->insert(Array)
#5 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Frontend.php(611): App\Models\FrontendModel->insertOrderDetails(Array)
#6 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Frontend->enterotp()
#7 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#10 {main}
CRITICAL - 2021-12-14 02:03:28 --> implode(): Invalid arguments passed
#0 [internal function]: CodeIgniter\Debug\Exceptions->errorHandler(2, 'implode(): Inva...', 'E:\\xampp\\htdocs...', 47, Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Frontend.php(47): implode(',', 'Skincare')
#2 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Frontend->index()
#3 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#4 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#5 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#6 {main}
CRITICAL - 2021-12-14 02:04:05 --> implode(): Invalid arguments passed
#0 [internal function]: CodeIgniter\Debug\Exceptions->errorHandler(2, 'implode(): Inva...', 'E:\\xampp\\htdocs...', 49, Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Frontend.php(49): implode(',', 'Bridal')
#2 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Frontend->index()
#3 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#4 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#5 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#6 {main}
CRITICAL - 2021-12-14 02:04:06 --> implode(): Invalid arguments passed
#0 [internal function]: CodeIgniter\Debug\Exceptions->errorHandler(2, 'implode(): Inva...', 'E:\\xampp\\htdocs...', 49, Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Frontend.php(49): implode(',', 'Bridal')
#2 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Frontend->index()
#3 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#4 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#5 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#6 {main}
CRITICAL - 2021-12-14 02:04:41 --> implode(): Invalid arguments passed
#0 [internal function]: CodeIgniter\Debug\Exceptions->errorHandler(2, 'implode(): Inva...', 'E:\\xampp\\htdocs...', 51, Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Frontend.php(51): implode(',', 'Bridal')
#2 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Frontend->index()
#3 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#4 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#5 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#6 {main}
CRITICAL - 2021-12-14 02:05:47 --> implode(): Invalid arguments passed
#0 [internal function]: CodeIgniter\Debug\Exceptions->errorHandler(2, 'implode(): Inva...', 'E:\\xampp\\htdocs...', 49, Array)
#1 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Frontend.php(49): implode(',', 'Skincare')
#2 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Frontend->index()
#3 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#4 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#5 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#6 {main}
