ERROR - 2021-12-18 03:38:35 --> mysqli_sql_exception: Column 'email' cannot be null in E:\xampp\htdocs\PROJECTS\baidees\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\xampp\htdocs\PROJECTS\baidees\system\Database\MySQLi\Connection.php(314): mysqli->query('INSERT INTO `tb...')
#1 E:\xampp\htdocs\PROJECTS\baidees\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `tb...')
#2 E:\xampp\htdocs\PROJECTS\baidees\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `tb...')
#3 E:\xampp\htdocs\PROJECTS\baidees\system\Database\BaseBuilder.php(2263): CodeIgniter\Database\BaseConnection->query('INSERT INTO `tb...', Array, false)
#4 E:\xampp\htdocs\PROJECTS\baidees\app\Models\FrontendModel.php(247): CodeIgniter\Database\BaseBuilder->insert(Array)
#5 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Frontend.php(676): App\Models\FrontendModel->insertOrderDetails(Array)
#6 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Frontend->enterotp()
#7 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2021-12-18 03:38:40 --> mysqli_sql_exception: Column 'email' cannot be null in E:\xampp\htdocs\PROJECTS\baidees\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\xampp\htdocs\PROJECTS\baidees\system\Database\MySQLi\Connection.php(314): mysqli->query('INSERT INTO `tb...')
#1 E:\xampp\htdocs\PROJECTS\baidees\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `tb...')
#2 E:\xampp\htdocs\PROJECTS\baidees\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `tb...')
#3 E:\xampp\htdocs\PROJECTS\baidees\system\Database\BaseBuilder.php(2263): CodeIgniter\Database\BaseConnection->query('INSERT INTO `tb...', Array, false)
#4 E:\xampp\htdocs\PROJECTS\baidees\app\Models\FrontendModel.php(247): CodeIgniter\Database\BaseBuilder->insert(Array)
#5 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Frontend.php(676): App\Models\FrontendModel->insertOrderDetails(Array)
#6 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Frontend->enterotp()
#7 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2021-12-18 03:44:00 --> mysqli_sql_exception: Column 'email' cannot be null in E:\xampp\htdocs\PROJECTS\baidees\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\xampp\htdocs\PROJECTS\baidees\system\Database\MySQLi\Connection.php(314): mysqli->query('INSERT INTO `tb...')
#1 E:\xampp\htdocs\PROJECTS\baidees\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `tb...')
#2 E:\xampp\htdocs\PROJECTS\baidees\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `tb...')
#3 E:\xampp\htdocs\PROJECTS\baidees\system\Database\BaseBuilder.php(2263): CodeIgniter\Database\BaseConnection->query('INSERT INTO `tb...', Array, false)
#4 E:\xampp\htdocs\PROJECTS\baidees\app\Models\FrontendModel.php(247): CodeIgniter\Database\BaseBuilder->insert(Array)
#5 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Frontend.php(676): App\Models\FrontendModel->insertOrderDetails(Array)
#6 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Frontend->enterotp()
#7 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#10 {main}
ERROR - 2021-12-18 03:56:33 --> mysqli_sql_exception: Column 'email' cannot be null in E:\xampp\htdocs\PROJECTS\baidees\system\Database\MySQLi\Connection.php:314
Stack trace:
#0 E:\xampp\htdocs\PROJECTS\baidees\system\Database\MySQLi\Connection.php(314): mysqli->query('INSERT INTO `tb...')
#1 E:\xampp\htdocs\PROJECTS\baidees\system\Database\BaseConnection.php(713): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `tb...')
#2 E:\xampp\htdocs\PROJECTS\baidees\system\Database\BaseConnection.php(641): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `tb...')
#3 E:\xampp\htdocs\PROJECTS\baidees\system\Database\BaseBuilder.php(2263): CodeIgniter\Database\BaseConnection->query('INSERT INTO `tb...', Array, false)
#4 E:\xampp\htdocs\PROJECTS\baidees\app\Models\FrontendModel.php(247): CodeIgniter\Database\BaseBuilder->insert(Array)
#5 E:\xampp\htdocs\PROJECTS\baidees\app\Controllers\Frontend.php(672): App\Models\FrontendModel->insertOrderDetails(Array)
#6 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(936): App\Controllers\Frontend->enterotp()
#7 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(432): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Frontend))
#8 E:\xampp\htdocs\PROJECTS\baidees\system\CodeIgniter.php(333): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 E:\xampp\htdocs\PROJECTS\baidees\index.php(45): CodeIgniter\CodeIgniter->run()
#10 {main}
