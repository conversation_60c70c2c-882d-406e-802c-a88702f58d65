<?php

// ok
somefunction1($foo, $bar, [
    // ...
], $baz);

// ok
$app->get('/hello/{name}', function ($name) use ($app) {
    return 'Hello '.$app->escape($name);
}, array(
    '1',
    '2',
    '3',
));

// error
somefunction2(
    $foo,
    $bar,
    [
    // ...
    ],
    $baz
);

// ok
somefunction3(// ...
    $foo,
    $bar,
    [
        // ...
    ],
    $baz
);

// ok
somefunction4('
    this should not
    give an error
    because it\'s actually
    one line call
    with multi-line string
');

// ok
somefunction5("hey,
multi-line string with some
extra args", $foo, 12);

// error
somefunction6(
    '
    but args in a new line
    are not ok…
    ',
    $foo
);

$this->setFoo(true
    ? 1
    : 2, false, array(
    'value',
    'more'));

$this->setFoo('some'
    . 'long'
    . 'text', 'string');

foo(bar(), $a);
foo();bar();

foo(
    true
);

myFunction(<<<END
Foo
END
);

var_dump(array(<<<'EOD'
foobar!
EOD
));

myFunction(<<<END
Foo
END
, 'bar');

myFunction(
    <<<END
Foo
END
    ,
    'bar'
);

if (array_filter(
    $commands,
    function ($cmd) use ($commandName) {
        return ($cmd['name'] == $commandName);
    }
)) {
    // Do something
}

myFunction(
    'foo',
    (object) array(
        'bar' => function ($x) {
            return true;
        },
        'baz' => false
    )
);
$qux = array_filter(
    $quux,
    function ($x) {
        return $x;
    }
);

$this->listeners[] = $events->getSharedManager()->attach(
    'Zend\Mvc\Application',
    MvcEvent::EVENT_DISPATCH,
    [$this, 'selectLayout'],
    100
);

// phpcs:set PSR2.Methods.FunctionCallSignature requiredSpacesBeforeClose 1
foo('Testing
    multiline text' );

foo('Testing
    multiline text: ' ); // . $text


foo('Testing
    multiline text: ' /* . $text */ );

foo('Testing
    multiline text: ' /* . $text */ );
    // . $other_text


foo('Testing
    multiline text: ' /*
 . $text
// . $text2
 */ );
// phpcs:set PSR2.Methods.FunctionCallSignature requiredSpacesBeforeClose 0

foo('Testing
    multiline text');

foo('Testing
    multiline text');

foo('Testing
    multiline text'); // hello


foo('Testing
    multiline text' /* hello */);

foo('Testing
    multiline text');
    // hello


foo('Testing
    multiline text'
    /* hello */);

$var = foo('Testing
    multiline')
    // hi
 + foo('Testing
    multiline');
    // hi


class Test
{
    public function getInstance()
    {
        return new static(
            'arg',
            'foo'
        );
    }

    public function getSelf()
    {
        return new self(
            'a',
            'b',
            'c'
        );
    }
}

$x = $var(
    'y',
    'x'
);

$obj->{$x}(
    1,
    2
);

(function ($a, $b) {
    return function ($c, $d) use ($a, $b) {
        echo $a, $b, $c, $d;
    };
})(
    'a',
    'b'
)(
    'c',
    'd'
);

return trim(preg_replace_callback(
            // sprintf replaces IGNORED_CHARS multiple times: for %s as well as %1$s (argument numbering)
            // /[%s]*([^%1$s]+)/ results in /[IGNORED_CHARS]*([^IGNORED_CHARS]+)/
    sprintf('/[%s]*([^%1$s]+)/', self::IGNORED_CHARS),
    function (array $term) use ($mode): string {
        // query pieces have to bigger than one char, otherwise they are too expensive for the search
        if (mb_strlen($term[1], 'UTF-8') > 1) {
            // in boolean search mode '' (empty) means OR, '-' means NOT
            return sprintf('%s%s ', $mode === 'AND' ? '+' : '', self::extractUmlauts($term[1]));
        }

        return '';
    },
    $search
));

return trim(preg_replace_callback(
// sprintf replaces IGNORED_CHARS multiple times: for %s as well as %1$s (argument numbering)
// /[%s]*([^%1$s]+)/ results in /[IGNORED_CHARS]*([^IGNORED_CHARS]+)/
    sprintf('/[%s]*([^%1$s]+)/', self::IGNORED_CHARS),
    function (array $term) use ($mode): string {
        // query pieces have to bigger than one char, otherwise they are too expensive for the search
        if (mb_strlen($term[1], 'UTF-8') > 1) {
            // in boolean search mode '' (empty) means OR, '-' means NOT
            return sprintf('%s%s ', $mode === 'AND' ? '+' : '', self::extractUmlauts($term[1]));
        }

        return '';
    },
    $search
));

// PHP 8.0 named parameters.
array_fill_keys(
    keys: range(
        1,
        12,
    ),
    value: true,
);

array_fill_keys(
    keys: range(
        1,
        12,
    ),
    value: true,
);

// phpcs:set PSR2.Methods.FunctionCallSignature allowMultipleArguments true
array_fill_keys(
    keys: range(
        1,
        12,
    ), value: true,
);
// phpcs:set PSR2.Methods.FunctionCallSignature allowMultipleArguments false
